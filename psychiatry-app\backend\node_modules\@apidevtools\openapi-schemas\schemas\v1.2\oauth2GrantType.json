{"id": "https://raw.githubusercontent.com/OAI/OpenAPI-Specification/master/schemas/v1.2/oauth2GrantType.json#", "$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "minProperties": 1, "properties": {"implicit": {"$ref": "#/definitions/implicit"}, "authorization_code": {"$ref": "#/definitions/authorizationCode"}}, "definitions": {"implicit": {"type": "object", "required": ["loginEndpoint"], "properties": {"loginEndpoint": {"$ref": "#/definitions/loginEndpoint"}, "tokenName": {"type": "string"}}, "additionalProperties": false}, "authorizationCode": {"type": "object", "required": ["tokenEndpoint", "tokenRequestEndpoint"], "properties": {"tokenEndpoint": {"$ref": "#/definitions/tokenEndpoint"}, "tokenRequestEndpoint": {"$ref": "#/definitions/tokenRequestEndpoint"}}, "additionalProperties": false}, "loginEndpoint": {"type": "object", "required": ["url"], "properties": {"url": {"type": "string", "format": "uri"}}, "additionalProperties": false}, "tokenEndpoint": {"type": "object", "required": ["url"], "properties": {"url": {"type": "string", "format": "uri"}, "tokenName": {"type": "string"}}, "additionalProperties": false}, "tokenRequestEndpoint": {"type": "object", "required": ["url"], "properties": {"url": {"type": "string", "format": "uri"}, "clientIdName": {"type": "string"}, "clientSecretName": {"type": "string"}}, "additionalProperties": false}}}