"use strict";
exports.__esModule = true;
exports.OpenAPIV2 = exports.OpenAPIV3 = void 0;
var OpenAPIV3;
(function (OpenAPIV3) {
    // All HTTP methods allowed by OpenAPI 3 spec
    // See https://swagger.io/specification/#path-item-object
    // You can use keys or values from it in TypeScript code like this:
    //     for (const method of Object.values(OpenAPIV3.HttpMethods)) { … }
    var HttpMethods;
    (function (HttpMethods) {
        HttpMethods["GET"] = "get";
        HttpMethods["PUT"] = "put";
        HttpMethods["POST"] = "post";
        HttpMethods["DELETE"] = "delete";
        HttpMethods["OPTIONS"] = "options";
        HttpMethods["HEAD"] = "head";
        HttpMethods["PATCH"] = "patch";
        HttpMethods["TRACE"] = "trace";
    })(HttpMethods = OpenAPIV3.HttpMethods || (OpenAPIV3.HttpMethods = {}));
})(OpenAPIV3 = exports.OpenAPIV3 || (exports.OpenAPIV3 = {}));
var OpenAPIV2;
(function (OpenAPIV2) {
    // All HTTP methods allowed by OpenAPI 2 spec
    // See https://swagger.io/specification/v2#path-item-object
    // You can use keys or values from it in TypeScript code like this:
    //     for (const method of Object.values(OpenAPIV2.HttpMethods)) { … }
    var HttpMethods;
    (function (HttpMethods) {
        HttpMethods["GET"] = "get";
        HttpMethods["PUT"] = "put";
        HttpMethods["POST"] = "post";
        HttpMethods["DELETE"] = "delete";
        HttpMethods["OPTIONS"] = "options";
        HttpMethods["HEAD"] = "head";
        HttpMethods["PATCH"] = "patch";
    })(HttpMethods = OpenAPIV2.HttpMethods || (OpenAPIV2.HttpMethods = {}));
})(OpenAPIV2 = exports.OpenAPIV2 || (exports.OpenAPIV2 = {}));
//# sourceMappingURL=index.js.map