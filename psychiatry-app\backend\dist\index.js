"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const dotenv_1 = __importDefault(require("dotenv"));
const security_1 = require("@/middleware/security");
const errorHandler_1 = require("@/middleware/errorHandler");
const swagger_1 = require("@/config/swagger");
const auth_1 = __importDefault(require("@/routes/auth"));
const patients_1 = __importDefault(require("@/routes/patients"));
const labResults_1 = __importDefault(require("@/routes/labResults"));
const appointments_1 = __importDefault(require("@/routes/appointments"));
const notifications_1 = __importDefault(require("@/routes/notifications"));
const analytics_1 = __importDefault(require("@/routes/analytics"));
const assessmentSessions_1 = __importDefault(require("@/routes/assessmentSessions"));
const health_1 = __importDefault(require("@/routes/health"));
dotenv_1.default.config();
const requiredEnvVars = ['DATABASE_URL', 'JWT_SECRET', 'JWT_REFRESH_SECRET'];
const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
if (missingEnvVars.length > 0) {
    console.error(`❌ Missing required environment variables: ${missingEnvVars.join(', ')}`);
    console.error('Please check your .env file');
    process.exit(1);
}
const app = (0, express_1.default)();
const PORT = process.env.PORT || 3001;
app.set('trust proxy', 1);
app.use(security_1.securityHeaders);
app.use(security_1.generalRateLimit);
app.use((0, cors_1.default)(security_1.corsOptions));
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
app.use(security_1.sanitizeInput);
if (process.env.NODE_ENV === 'development') {
    app.use(security_1.requestLogger);
}
(0, swagger_1.setupSwagger)(app);
app.get('/health', (req, res) => {
    res.status(200).json({
        success: true,
        message: 'Server is healthy',
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV,
    });
});
app.use('/api/auth', auth_1.default);
app.use('/api/patients', patients_1.default);
app.use('/api/lab-results', labResults_1.default);
app.use('/api/appointments', appointments_1.default);
app.use('/api/analytics', analytics_1.default);
app.use('/api/notifications', notifications_1.default);
app.use('/api/assessment-sessions', assessmentSessions_1.default);
app.use('/api/health', health_1.default);
app.get('/api', (req, res) => {
    res.status(200).json({
        success: true,
        message: 'Psychiatry Patient Management API',
        version: '1.0.0',
        documentation: '/api/docs',
        endpoints: {
            auth: {
                register: 'POST /api/auth/register',
                login: 'POST /api/auth/login',
                refresh: 'POST /api/auth/refresh',
                logout: 'POST /api/auth/logout',
                me: 'GET /api/auth/me',
                changePassword: 'PUT /api/auth/change-password',
            },
            patients: {
                list: 'GET /api/patients',
                create: 'POST /api/patients',
                get: 'GET /api/patients/:id',
                update: 'PUT /api/patients/:id',
                delete: 'DELETE /api/patients/:id',
                stats: 'GET /api/patients/stats',
                labResults: 'GET /api/patients/:patientId/lab-results',
                labTrends: 'GET /api/patients/:patientId/lab-results/trends',
                createAssessmentSession: 'POST /api/patients/:patientId/assessment-sessions',
                getAssessmentSessions: 'GET /api/patients/:patientId/assessment-sessions',
            },
            assessment_sessions: {
                getById: 'GET /api/assessment-sessions/:id',
            },
            labResults: {
                list: 'GET /api/lab-results',
                create: 'POST /api/lab-results',
                get: 'GET /api/lab-results/:id',
                delete: 'DELETE /api/lab-results/:id',
            },
            appointments: {
                list: 'GET /api/appointments',
                create: 'POST /api/appointments',
                get: 'GET /api/appointments/:id',
                update: 'PUT /api/appointments/:id',
                cancel: 'POST /api/appointments/:id/cancel',
                stats: 'GET /api/appointments/stats',
                availability: 'GET /api/appointments/providers/:providerId/availability',
            },
            notifications: {
                list: 'GET /api/notifications',
                create: 'POST /api/notifications',
                markRead: 'PUT /api/notifications/:id/read',
                stats: 'GET /api/notifications/stats',
                appointmentReminders: 'POST /api/notifications/appointment-reminders',
                labResult: 'POST /api/notifications/lab-result',
                processScheduled: 'POST /api/notifications/process-scheduled',
            },
            analytics: {
                dashboard: 'GET /api/analytics/dashboard',
                patients: 'GET /api/analytics/patients',
                appointments: 'GET /api/analytics/appointments',
                labResults: 'GET /api/analytics/lab-results',
                system: 'GET /api/analytics/system',
            },
        },
    });
});
app.use(errorHandler_1.notFoundHandler);
app.use(errorHandler_1.errorHandler);
const server = app.listen(PORT, () => {
    console.log(`🚀 Server running on port ${PORT}`);
    console.log(`📊 Environment: ${process.env.NODE_ENV}`);
    console.log(`🔗 API Base URL: http://localhost:${PORT}/api`);
    console.log(`🏥 Health Check: http://localhost:${PORT}/health`);
});
process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    server.close(() => {
        console.log('Process terminated');
    });
});
process.on('SIGINT', () => {
    console.log('SIGINT received, shutting down gracefully');
    server.close(() => {
        console.log('Process terminated');
    });
});
exports.default = app;
//# sourceMappingURL=index.js.map