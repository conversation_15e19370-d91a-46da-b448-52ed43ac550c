import { PrismaClient } from '@prisma/client';
import { PatientService } from '@/services/patientService';
import { LabResultService } from '@/services/labResultService';
import { AppointmentService } from '@/services/appointmentService';
import { createTestUser, createTestPatient, prisma } from '../setup';

describe('Database Transactions and Data Integrity Tests', () => {
  let testUser: any;

  beforeEach(async () => {
    testUser = await createTestUser('CLINICIAN');
  });

  describe('Transaction Rollback Tests', () => {
    test('should rollback patient creation on validation failure', async () => {
      const initialPatientCount = await prisma.patient.count();

      // Attempt to create patient with invalid data that should fail
      const invalidPatientData = {
        firstName: 'Test',
        lastName: 'Patient',
        dateOfBirth: new Date('2030-01-01'), // Future date - should be invalid
        gender: 'INVALID_GENDER', // Invalid gender
        email: 'invalid-email', // Invalid email format
      };

      try {
        await PatientService.createPatient(
          invalidPatientData as any,
          testUser.id
        );
        fail('Should have thrown validation error');
      } catch (error) {
        // Expected to fail
      }

      // Verify no patient was created
      const finalPatientCount = await prisma.patient.count();
      expect(finalPatientCount).toBe(initialPatientCount);
    });

    test('should maintain referential integrity when deleting patients', async () => {
      // Create patient with related data
      const patient = await createTestPatient(testUser.id);

      // Create lab result for patient
      const labResultData = {
        patientId: patient.id,
        testType: 'CBC',
        testDate: new Date(),
        orderedBy: 'Dr. Test',
        labName: 'Test Lab',
        results: JSON.stringify({ WBC: { value: 7.5, unit: 'K/uL' } }),
        normalRanges: JSON.stringify({ WBC: '4.0-11.0 K/uL' }),
        status: 'COMPLETED',
        notes: 'Test lab result'
      };

      const labResult = await LabResultService.createLabResult(
        labResultData,
        testUser.id
      );

      // Create appointment for patient
      const appointmentData = {
        patientId: patient.id,
        providerId: testUser.id,
        date: new Date(Date.now() + 24 * 60 * 60 * 1000),
        duration: 60,
        type: 'INITIAL_CONSULTATION' as const,
        status: 'SCHEDULED' as const,
        notes: 'Test appointment'
      };

      const appointment = await AppointmentService.createAppointment(
        appointmentData,
        testUser.id
      );

      // Soft delete patient
      await PatientService.deletePatient(patient.id, testUser.id, testUser.role);

      // Verify patient is soft deleted
      const deletedPatient = await prisma.patient.findUnique({
        where: { id: patient.id }
      });
      expect(deletedPatient?.isDeleted).toBe(true);

      // Verify related data still exists but is properly handled
      const relatedLabResult = await prisma.labResult.findUnique({
        where: { id: labResult.data.labResult.id }
      });
      expect(relatedLabResult).toBeTruthy();

      const relatedAppointment = await prisma.appointment.findUnique({
        where: { id: appointment.data.appointment.id }
      });
      expect(relatedAppointment).toBeTruthy();
    });
  });

  describe('Concurrent Access Tests', () => {
    test('should handle concurrent patient updates correctly', async () => {
      const patient = await createTestPatient(testUser.id);
      const anotherUser = await createTestUser('ADMIN');

      // Simulate concurrent updates
      const updatePromises = [
        PatientService.updatePatient(
          patient.id,
          { firstName: 'Updated1' },
          testUser.id,
          testUser.role
        ),
        PatientService.updatePatient(
          patient.id,
          { lastName: 'Updated2' },
          anotherUser.id,
          anotherUser.role
        )
      ];

      const results = await Promise.allSettled(updatePromises);

      // At least one should succeed
      const successfulUpdates = results.filter(result => result.status === 'fulfilled');
      expect(successfulUpdates.length).toBeGreaterThan(0);

      // Verify final state is consistent
      const finalPatient = await prisma.patient.findUnique({
        where: { id: patient.id }
      });
      expect(finalPatient).toBeTruthy();
    });

    test('should handle concurrent lab result creation for same patient', async () => {
      const patient = await createTestPatient(testUser.id);

      const labResultData1 = {
        patientId: patient.id,
        testType: 'CBC',
        testDate: new Date(),
        orderedBy: 'Dr. Test1',
        labName: 'Test Lab',
        results: JSON.stringify({ WBC: { value: 7.5 } }),
        normalRanges: JSON.stringify({ WBC: '4.0-11.0 K/uL' }),
        status: 'COMPLETED',
        notes: 'First lab result'
      };

      const labResultData2 = {
        patientId: patient.id,
        testType: 'LIPID_PANEL',
        testDate: new Date(),
        orderedBy: 'Dr. Test2',
        labName: 'Test Lab',
        results: JSON.stringify({ Cholesterol: { value: 200 } }),
        normalRanges: JSON.stringify({ Cholesterol: '<200 mg/dL' }),
        status: 'COMPLETED',
        notes: 'Second lab result'
      };

      // Create lab results concurrently
      const createPromises = [
        LabResultService.createLabResult(labResultData1, testUser.id),
        LabResultService.createLabResult(labResultData2, testUser.id)
      ];

      const results = await Promise.all(createPromises);

      // Both should succeed
      expect(results[0].success).toBe(true);
      expect(results[1].success).toBe(true);

      // Verify both lab results exist
      const labResults = await prisma.labResult.findMany({
        where: { patientId: patient.id }
      });
      expect(labResults).toHaveLength(2);
    });
  });

  describe('Data Consistency Tests', () => {
    test('should maintain audit trail consistency', async () => {
      const patient = await createTestPatient(testUser.id);

      // Update patient multiple times
      await PatientService.updatePatient(
        patient.id,
        { firstName: 'Updated1' },
        testUser.id,
        testUser.role
      );

      await PatientService.updatePatient(
        patient.id,
        { lastName: 'Updated2' },
        testUser.id,
        testUser.role
      );

      // Verify audit logs are created
      const auditLogs = await prisma.auditLog.findMany({
        where: {
          entityType: 'PATIENT',
          entityId: patient.id
        },
        orderBy: { createdAt: 'asc' }
      });

      // Should have CREATE, UPDATE, UPDATE logs
      expect(auditLogs.length).toBeGreaterThanOrEqual(3);
      expect(auditLogs[0].action).toBe('CREATE');
      expect(auditLogs[1].action).toBe('UPDATE');
      expect(auditLogs[2].action).toBe('UPDATE');
    });

    test('should enforce unique constraints', async () => {
      const patient1 = await createTestPatient(testUser.id);

      // Try to create another patient with same patientId
      const duplicatePatientData = {
        patientId: patient1.patientId, // Same ID
        firstName: 'Duplicate',
        lastName: 'Patient',
        dateOfBirth: new Date('1990-01-01'),
        gender: 'MALE',
        createdBy: testUser.id
      };

      await expect(
        prisma.patient.create({ data: duplicatePatientData })
      ).rejects.toThrow();
    });

    test('should validate foreign key constraints', async () => {
      // Try to create lab result with non-existent patient
      const invalidLabResultData = {
        patientId: 'non-existent-id',
        testType: 'CBC',
        testDate: new Date(),
        orderedBy: 'Dr. Test',
        labName: 'Test Lab',
        results: JSON.stringify({ WBC: { value: 7.5 } }),
        normalRanges: JSON.stringify({ WBC: '4.0-11.0 K/uL' }),
        status: 'COMPLETED',
        createdBy: testUser.id
      };

      await expect(
        prisma.labResult.create({ data: invalidLabResultData })
      ).rejects.toThrow();
    });
  });

  describe('Performance and Scalability Tests', () => {
    test('should handle bulk patient creation efficiently', async () => {
      const startTime = Date.now();
      const patientPromises = [];

      // Create 10 patients concurrently
      for (let i = 0; i < 10; i++) {
        const patientData = {
          firstName: `Patient${i}`,
          lastName: 'Bulk',
          dateOfBirth: new Date('1990-01-01'),
          gender: 'MALE',
          email: `patient${i}@example.com`,
          createdBy: testUser.id
        };

        patientPromises.push(
          PatientService.createPatient(patientData, testUser.id)
        );
      }

      const results = await Promise.all(patientPromises);
      const endTime = Date.now();

      // All should succeed
      results.forEach(result => {
        expect(result.success).toBe(true);
      });

      // Should complete within reasonable time (adjust threshold as needed)
      const duration = endTime - startTime;
      expect(duration).toBeLessThan(5000); // 5 seconds

      // Verify all patients were created
      const patientCount = await prisma.patient.count({
        where: { lastName: 'Bulk' }
      });
      expect(patientCount).toBe(10);
    });
  });
});
