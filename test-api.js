const http = require('http');

function testEndpoint(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3002,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const parsed = JSON.parse(body);
          resolve({
            status: res.statusCode,
            data: parsed
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: body
          });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function runTests() {
  console.log('🧪 Testing API endpoints...\n');

  try {
    // Test 1: Health check
    console.log('1. Testing health endpoint...');
    const health = await testEndpoint('/health');
    console.log(`   Status: ${health.status}`);
    console.log(`   Response: ${health.data.success ? '✅ Healthy' : '❌ Unhealthy'}\n`);

    // Test 2: Get all patients
    console.log('2. Testing patients list...');
    const patients = await testEndpoint('/api/patients');
    console.log(`   Status: ${patients.status}`);
    if (patients.status === 200 && patients.data.data && patients.data.data.patients) {
      console.log(`   Found ${patients.data.data.patients.length} patients`);
      if (patients.data.data.patients.length > 0) {
        const firstPatient = patients.data.data.patients[0];
        console.log(`   First patient: ${firstPatient.patientId} - ${firstPatient.firstName} ${firstPatient.lastName}`);
      }
    } else {
      console.log(`   Error or unexpected response structure:`);
      console.log(`   ${JSON.stringify(patients.data, null, 2)}`);
    }
    console.log('');

    // Test 3: Get specific patient by ID (P-2025-001)
    console.log('3. Testing patient by ID (P-2025-001)...');
    const patient = await testEndpoint('/api/patients/P-2025-001');
    console.log(`   Status: ${patient.status}`);
    if (patient.status === 200) {
      console.log(`   ✅ Patient found: ${patient.data.data.patient.firstName} ${patient.data.data.patient.lastName}`);
    } else {
      console.log(`   ❌ Patient not found: ${JSON.stringify(patient.data)}`);
    }
    console.log('');

    // Test 3b: Get specific patient by UUID
    console.log('3b. Testing patient by UUID (d881afba-c2ca-43cd-98c7-14e95bfcb6fc)...');
    const patientByUuid = await testEndpoint('/api/patients/d881afba-c2ca-43cd-98c7-14e95bfcb6fc');
    console.log(`   Status: ${patientByUuid.status}`);
    if (patientByUuid.status === 200) {
      console.log(`   ✅ Patient found by UUID: ${patientByUuid.data.data.patient.firstName} ${patientByUuid.data.data.patient.lastName}`);
    } else {
      console.log(`   ❌ Patient not found by UUID: ${JSON.stringify(patientByUuid.data)}`);
    }
    console.log('');

    // Test 4: Test assessment sessions endpoint
    console.log('4. Testing assessment sessions endpoint...');
    const assessments = await testEndpoint('/api/patients/P-2025-001/assessment-sessions');
    console.log(`   Status: ${assessments.status}`);
    if (assessments.status === 200) {
      console.log(`   ✅ Assessment sessions accessible`);
    } else {
      console.log(`   ❌ Assessment sessions error: ${JSON.stringify(assessments.data)}`);
    }
    console.log('');

    // Test 5: Test lab results endpoint
    console.log('5. Testing lab results GET endpoint...');
    const labResults = await testEndpoint('/api/lab-results');
    console.log(`   Status: ${labResults.status}`);
    if (labResults.status === 200) {
      console.log(`   ✅ Lab results GET accessible`);
    } else {
      console.log(`   ❌ Lab results GET error: ${JSON.stringify(labResults.data)}`);
    }
    console.log('');

    // Test 6: Test lab results creation (POST)
    console.log('6. Testing lab results POST endpoint...');
    const labResultData = {
      patientId: 'd881afba-c2ca-43cd-98c7-14e95bfcb6fc',
      testType: 'Blood Test',
      testDate: '2025-07-18',
      orderedBy: 'Dr. Test',
      results: {
        hemoglobin: 12.5,
        whiteBloodCells: 7000
      },
      status: 'completed',
      notes: 'Test lab result creation'
    };
    const createLabResult = await testEndpoint('/api/lab-results', 'POST', labResultData);
    console.log(`   Status: ${createLabResult.status}`);
    if (createLabResult.status === 201) {
      console.log(`   ✅ Lab result created successfully`);
    } else {
      console.log(`   ❌ Lab result creation error: ${JSON.stringify(createLabResult.data)}`);
    }
    console.log('');

    // Test 7: Test assessment session creation (POST)
    console.log('7. Testing assessment session creation...');
    const assessmentData = {
      sessionDate: '2025-07-18T10:00:00Z',
      assessments: [{
        disorderId: 'test-disorder',
        patientId: 'd881afba-c2ca-43cd-98c7-14e95bfcb6fc',
        assessmentDate: '2025-07-18',
        criteria: [],
        severity: 'mild',
        diagnosticConfidence: 'provisional'
      }],
      riskAssessment: {
        suicidalIdeation: false,
        homicidalIdeation: false,
        selfHarm: false,
        substanceUse: false,
        level: 'low'
      },
      status: 'completed',
      duration: 60
    };
    const createAssessment = await testEndpoint('/api/patients/d881afba-c2ca-43cd-98c7-14e95bfcb6fc/assessment-sessions', 'POST', assessmentData);
    console.log(`   Status: ${createAssessment.status}`);
    if (createAssessment.status === 201) {
      console.log(`   ✅ Assessment session created successfully`);
    } else {
      console.log(`   ❌ Assessment session creation error: ${JSON.stringify(createAssessment.data)}`);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

runTests();
