import { Request, Response, NextFunction } from 'express';
import { createHash } from 'crypto';

// Extend Request interface to include user
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    role: string;
    email: string;
  };
}

/**
 * Simple in-memory cache for clinical data
 * In production, consider using Redis for distributed caching
 */

interface CacheEntry {
  data: any;
  timestamp: number;
  ttl: number;
}

class MemoryCache {
  private cache: Map<string, CacheEntry> = new Map();
  private maxSize: number = 1000; // Maximum number of cached items
  private defaultTTL: number = 5 * 60 * 1000; // 5 minutes default TTL

  constructor(maxSize?: number, defaultTTL?: number) {
    if (maxSize) this.maxSize = maxSize;
    if (defaultTTL) this.defaultTTL = defaultTTL;
    
    // Clean up expired entries every minute
    setInterval(() => this.cleanup(), 60 * 1000);
  }

  /**
   * Generate cache key from request
   */
  private generateKey(req: AuthenticatedRequest): string {
    const keyData = {
      method: req.method,
      url: req.originalUrl,
      query: req.query,
      userId: req.user?.id || 'anonymous'
    };
    
    return createHash('md5').update(JSON.stringify(keyData)).digest('hex');
  }

  /**
   * Get cached data
   */
  get(key: string): any | null {
    const entry = this.cache.get(key);
    
    if (!entry) return null;
    
    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.data;
  }

  /**
   * Set cached data
   */
  set(key: string, data: any, ttl?: number): void {
    // Implement LRU eviction if cache is full
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL
    });
  }

  /**
   * Delete cached data
   */
  delete(key: string): void {
    this.cache.delete(key);
  }

  /**
   * Clear all cached data
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Get cache statistics
   */
  getStats(): { size: number; maxSize: number; hitRate?: number } {
    return {
      size: this.cache.size,
      maxSize: this.maxSize
    };
  }

  /**
   * Invalidate cache entries by pattern
   */
  invalidatePattern(pattern: string): void {
    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        this.cache.delete(key);
      }
    }
  }
}

// Global cache instance
const cache = new MemoryCache();

/**
 * Cache middleware for GET requests
 */
export const cacheMiddleware = (ttl?: number) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    // Only cache GET requests
    if (req.method !== 'GET') {
      return next();
    }

    // Don't cache if user is not authenticated (for security)
    if (!req.user) {
      return next();
    }

    const key = cache['generateKey'](req);
    const cachedData = cache.get(key);

    if (cachedData) {
      // Add cache headers
      res.set({
        'X-Cache': 'HIT',
        'X-Cache-Key': key
      });
      
      return res.json(cachedData);
    }

    // Store original json method
    const originalJson = res.json;

    // Override json method to cache the response
    res.json = function(data: any) {
      // Only cache successful responses
      if (res.statusCode === 200 && data?.success) {
        cache.set(key, data, ttl);
      }

      // Add cache headers
      res.set({
        'X-Cache': 'MISS',
        'X-Cache-Key': key
      });

      // Call original json method
      return originalJson.call(this, data);
    };

    next();
  };
};

/**
 * Cache invalidation middleware for data-modifying operations
 */
export const invalidateCacheMiddleware = (patterns: string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    // Store original json method
    const originalJson = res.json;

    // Override json method to invalidate cache after successful operations
    res.json = function(data: any) {
      // Only invalidate cache for successful operations
      if (res.statusCode < 400 && data?.success) {
        patterns.forEach(pattern => {
          cache.invalidatePattern(pattern);
        });
      }

      // Call original json method
      return originalJson.call(this, data);
    };

    next();
  };
};

/**
 * Specific cache configurations for different endpoints
 */

// Patient data cache (5 minutes)
export const patientCache = cacheMiddleware(5 * 60 * 1000);

// Lab results cache (10 minutes - less frequently changing)
export const labResultCache = cacheMiddleware(10 * 60 * 1000);

// Appointment cache (2 minutes - frequently changing)
export const appointmentCache = cacheMiddleware(2 * 60 * 1000);

// Analytics cache (30 minutes - expensive queries)
export const analyticsCache = cacheMiddleware(30 * 60 * 1000);

// Statistics cache (15 minutes)
export const statsCache = cacheMiddleware(15 * 60 * 1000);

/**
 * Cache invalidation patterns for different operations
 */

// Invalidate patient-related caches
export const invalidatePatientCache = invalidateCacheMiddleware([
  'patients',
  'analytics',
  'stats'
]);

// Invalidate lab result caches
export const invalidateLabResultCache = invalidateCacheMiddleware([
  'lab-results',
  'patients',
  'analytics'
]);

// Invalidate appointment caches
export const invalidateAppointmentCache = invalidateCacheMiddleware([
  'appointments',
  'analytics',
  'stats'
]);

/**
 * Manual cache management functions
 */
export const cacheManager = {
  /**
   * Get cache statistics
   */
  getStats: () => cache.getStats(),

  /**
   * Clear all cache
   */
  clearAll: () => cache.clear(),

  /**
   * Invalidate specific patterns
   */
  invalidate: (patterns: string[]) => {
    patterns.forEach(pattern => cache.invalidatePattern(pattern));
  },

  /**
   * Warm up cache with frequently accessed data
   */
  warmUp: async () => {
    // This would be implemented to pre-load frequently accessed data
    console.log('Cache warm-up not implemented yet');
  }
};

/**
 * Cache health check
 */
export const getCacheHealth = () => {
  const stats = cache.getStats();
  const memoryUsage = process.memoryUsage();
  
  return {
    cache: {
      size: stats.size,
      maxSize: stats.maxSize,
      utilization: Math.round((stats.size / stats.maxSize) * 100),
      status: stats.size < stats.maxSize * 0.9 ? 'healthy' : 'near_full'
    },
    memory: {
      heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
      heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
      external: Math.round(memoryUsage.external / 1024 / 1024)
    }
  };
};

export default cache;
