import request from 'supertest';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// This test assumes the server is running on localhost:3002
const API_BASE = 'http://localhost:3002';

describe('End-to-End Clinical Workflow Tests', () => {
  let authToken: string;
  let userId: string;
  let patientId: string;
  let labResultId: string;
  let appointmentId: string;

  beforeAll(async () => {
    // Clean up test data
    await cleanupTestData();
  });

  afterAll(async () => {
    // Clean up test data
    await cleanupTestData();
    await prisma.$disconnect();
  });

  describe('Complete Clinical Workflow', () => {
    test('1. User Registration and Authentication', async () => {
      // Register a new clinician
      const registerResponse = await request(API_BASE)
        .post('/api/auth/register')
        .send({
          username: `clinician_${Date.now()}`,
          email: `clinician_${Date.now()}@test.com`,
          password: 'SecurePassword123!',
          firstName: 'Dr. Test',
          lastName: 'Clinician',
          role: 'CLINICIAN'
        });

      expect(registerResponse.status).toBe(201);
      expect(registerResponse.body.success).toBe(true);
      expect(registerResponse.body.data.user.role).toBe('CLINICIAN');

      userId = registerResponse.body.data.user.id;

      // Login with the new user
      const loginResponse = await request(API_BASE)
        .post('/api/auth/login')
        .send({
          username: registerResponse.body.data.user.username,
          password: 'SecurePassword123!'
        });

      expect(loginResponse.status).toBe(200);
      expect(loginResponse.body.success).toBe(true);
      expect(loginResponse.body.data.accessToken).toBeDefined();

      authToken = loginResponse.body.data.accessToken;
    });

    test('2. Patient Creation', async () => {
      const patientData = {
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1985-06-15',
        gender: 'MALE',
        phone: '+**********',
        email: '<EMAIL>',
        address: JSON.stringify({
          street: '123 Test Street',
          city: 'Test City',
          state: 'TS',
          zipCode: '12345',
          country: 'USA'
        }),
        occupation: 'Software Engineer',
        education: 'BACHELORS',
        maritalStatus: 'SINGLE',
        emergencyContact: JSON.stringify({
          name: 'Jane Doe',
          relationship: 'Sister',
          phone: '+**********'
        }),
        insuranceInfo: JSON.stringify({
          provider: 'Test Insurance',
          policyNumber: 'TEST123456',
          groupNumber: 'GRP789'
        }),
        medicalHistory: JSON.stringify({
          allergies: ['Penicillin'],
          medications: ['Vitamin D'],
          conditions: ['Hypertension']
        })
      };

      const response = await request(API_BASE)
        .post('/api/patients')
        .set('Authorization', `Bearer ${authToken}`)
        .send(patientData);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.patient.firstName).toBe('John');
      expect(response.body.data.patient.lastName).toBe('Doe');
      expect(response.body.data.patient.patientId).toMatch(/^P-\d{4}-\d{3}$/);

      patientId = response.body.data.patient.id;
    });

    test('3. Lab Result Creation', async () => {
      const labResultData = {
        patientId: patientId,
        testType: 'CBC',
        testDate: new Date().toISOString(),
        orderedBy: 'Dr. Test Physician',
        labName: 'Test Laboratory',
        results: JSON.stringify({
          WBC: { value: 7.5, unit: 'K/uL', normalRange: '4.0-11.0', isNormal: true },
          RBC: { value: 4.5, unit: 'M/uL', normalRange: '4.2-5.4', isNormal: true },
          Hemoglobin: { value: 14.2, unit: 'g/dL', normalRange: '12.0-16.0', isNormal: true },
          Hematocrit: { value: 42.1, unit: '%', normalRange: '36.0-46.0', isNormal: true },
          Platelets: { value: 250, unit: 'K/uL', normalRange: '150-450', isNormal: true }
        }),
        normalRanges: JSON.stringify({
          WBC: '4.0-11.0 K/uL',
          RBC: '4.2-5.4 M/uL',
          Hemoglobin: '12.0-16.0 g/dL',
          Hematocrit: '36.0-46.0 %',
          Platelets: '150-450 K/uL'
        }),
        status: 'COMPLETED',
        notes: 'Normal CBC results - all values within normal limits'
      };

      const response = await request(API_BASE)
        .post('/api/lab-results')
        .set('Authorization', `Bearer ${authToken}`)
        .send(labResultData);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.labResult.testType).toBe('CBC');
      expect(response.body.data.labResult.status).toBe('COMPLETED');

      labResultId = response.body.data.labResult.id;
    });

    test('4. Appointment Scheduling', async () => {
      const appointmentDate = new Date();
      appointmentDate.setDate(appointmentDate.getDate() + 7); // Next week

      const appointmentData = {
        patientId: patientId,
        providerId: userId,
        date: appointmentDate.toISOString(),
        duration: 60,
        type: 'FOLLOW_UP',
        status: 'SCHEDULED',
        notes: 'Follow-up appointment to discuss lab results'
      };

      const response = await request(API_BASE)
        .post('/api/appointments')
        .set('Authorization', `Bearer ${authToken}`)
        .send(appointmentData);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.appointment.type).toBe('FOLLOW_UP');
      expect(response.body.data.appointment.status).toBe('SCHEDULED');

      appointmentId = response.body.data.appointment.id;
    });

    test('5. Data Retrieval and Verification', async () => {
      // Get patient with all related data
      const patientResponse = await request(API_BASE)
        .get(`/api/patients/${patientId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(patientResponse.status).toBe(200);
      expect(patientResponse.body.success).toBe(true);
      expect(patientResponse.body.data.patient.id).toBe(patientId);

      // Get patient's lab results
      const labResultsResponse = await request(API_BASE)
        .get(`/api/lab-results?patientId=${patientId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(labResultsResponse.status).toBe(200);
      expect(labResultsResponse.body.success).toBe(true);
      expect(labResultsResponse.body.data.length).toBeGreaterThan(0);

      // Get patient's appointments
      const appointmentsResponse = await request(API_BASE)
        .get(`/api/appointments?patientId=${patientId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(appointmentsResponse.status).toBe(200);
      expect(appointmentsResponse.body.success).toBe(true);
      expect(appointmentsResponse.body.data.length).toBeGreaterThan(0);
    });

    test('6. Search and Analytics', async () => {
      // Search for the patient
      const searchResponse = await request(API_BASE)
        .get(`/api/patients/search?term=John`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(searchResponse.status).toBe(200);
      expect(searchResponse.body.success).toBe(true);
      expect(searchResponse.body.data.length).toBeGreaterThan(0);

      // Get analytics data
      const analyticsResponse = await request(API_BASE)
        .get('/api/analytics/patients')
        .set('Authorization', `Bearer ${authToken}`);

      expect(analyticsResponse.status).toBe(200);
      expect(analyticsResponse.body.success).toBe(true);
    });

    test('7. Health Check Endpoints', async () => {
      // Basic health check
      const healthResponse = await request(API_BASE)
        .get('/health');

      expect(healthResponse.status).toBe(200);
      expect(healthResponse.body.success).toBe(true);
      expect(healthResponse.body.database).toBe('connected');
    });

    test('8. API Documentation', async () => {
      // Check if Swagger documentation is accessible
      const docsResponse = await request(API_BASE)
        .get('/api/docs.json');

      expect(docsResponse.status).toBe(200);
      expect(docsResponse.body.openapi).toBeDefined();
      expect(docsResponse.body.info.title).toContain('Psychiatry');
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('Should handle invalid patient data', async () => {
      const invalidPatientData = {
        firstName: '', // Empty required field
        lastName: 'Test',
        dateOfBirth: '2030-01-01', // Future date
        gender: 'INVALID_GENDER'
      };

      const response = await request(API_BASE)
        .post('/api/patients')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidPatientData);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    test('Should handle unauthorized access', async () => {
      const response = await request(API_BASE)
        .get('/api/patients')
        .set('Authorization', 'Bearer invalid-token');

      expect(response.status).toBe(401);
    });

    test('Should handle non-existent resources', async () => {
      const response = await request(API_BASE)
        .get('/api/patients/non-existent-id')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(404);
    });
  });

  // Helper function to clean up test data
  async function cleanupTestData() {
    try {
      // Delete test data in correct order to respect foreign key constraints
      await prisma.appointment.deleteMany({
        where: {
          patient: {
            email: { contains: '@test.com' }
          }
        }
      });

      await prisma.labResult.deleteMany({
        where: {
          patient: {
            email: { contains: '@test.com' }
          }
        }
      });

      await prisma.patient.deleteMany({
        where: {
          email: { contains: '@test.com' }
        }
      });

      await prisma.user.deleteMany({
        where: {
          email: { contains: '@test.com' }
        }
      });
    } catch (error) {
      console.warn('Cleanup warning:', error);
    }
  }
});
