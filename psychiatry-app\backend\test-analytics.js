const axios = require('axios');

const API_BASE_URL = 'http://localhost:3002';

async function testAnalytics() {
  try {
    console.log('🧪 Testing Analytics endpoint...\n');

    // Login to get authentication token
    console.log('1️⃣ Logging in...');
    const loginResponse = await axios.post(`${API_BASE_URL}/api/auth/login`, {
      username: 'dr.smith',
      password: 'clinician123!'
    });

    if (!loginResponse.data.success) {
      console.log('❌ Login failed:', loginResponse.data.message);
      return;
    }

    console.log('✅ Login successful');
    const token = loginResponse.data.data.accessToken;
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // Test analytics endpoint
    console.log('\n2️⃣ Testing analytics endpoint...');
    try {
      const analyticsResponse = await axios.get(
        `${API_BASE_URL}/api/analytics/lab-results?from=2024-01-01&to=2025-12-31`,
        { headers }
      );
      
      if (analyticsResponse.data.success) {
        console.log('✅ Analytics endpoint working!');
        console.log('📊 Analytics data structure:');
        console.log('   - Overview:', Object.keys(analyticsResponse.data.data.analytics.overview));
        console.log('   - Quality:', Object.keys(analyticsResponse.data.data.analytics.quality));
        console.log('   - Trends:', Object.keys(analyticsResponse.data.data.analytics.trends));
        
        // Check for BigInt issues by examining the data
        const analytics = analyticsResponse.data.data.analytics;
        console.log('\n📈 Sample data:');
        console.log('   - Total lab results:', analytics.overview.total);
        console.log('   - Monthly trend entries:', analytics.trends.monthly.length);
        console.log('   - Flagged results entries:', analytics.quality.flaggedByTestType.length);
        
        if (analytics.trends.monthly.length > 0) {
          console.log('   - First monthly entry:', analytics.trends.monthly[0]);
        }
        
        if (analytics.quality.flaggedByTestType.length > 0) {
          console.log('   - First flagged entry:', analytics.quality.flaggedByTestType[0]);
        }
      } else {
        console.log('❌ Analytics endpoint failed:', analyticsResponse.data.message);
      }
    } catch (analyticsError) {
      console.log('❌ Analytics endpoint error:', analyticsError.response?.data?.error || analyticsError.message);
      console.log('   Full error:', analyticsError.response?.data);
    }

  } catch (error) {
    console.error('❌ Test error:', error.response?.data || error.message);
  }
}

testAnalytics();
