import express from 'express';
import cors from 'cors';
// import morgan from 'morgan'; // Temporarily disabled due to network issues
import dotenv from 'dotenv';

// Import middleware
import { securityHeaders, generalRateLimit, sanitizeInput, corsOptions, requestLogger } from '@/middleware/security';
import { errorHand<PERSON>, notFoundHandler } from '@/middleware/errorHandler';
import { setupSwagger } from '@/config/swagger';

// Import routes
import authRoutes from '@/routes/auth';
import patientRoutes from '@/routes/patients';
import labResultRoutes from '@/routes/labResults';
import appointmentRoutes from '@/routes/appointments';
import notificationRoutes from '@/routes/notifications';
import analyticsRoutes from '@/routes/analytics';
import assessmentSessionRoutes from '@/routes/assessmentSessions';
import healthRoutes from '@/routes/health';

// Load environment variables
dotenv.config();

// Validate required environment variables
const requiredEnvVars = ['DATABASE_URL', 'JWT_SECRET', 'JWT_REFRESH_SECRET'];
const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  console.error(`❌ Missing required environment variables: ${missingEnvVars.join(', ')}`);
  console.error('Please check your .env file');
  process.exit(1);
}

/**
 * Express application setup with security middleware and routes
 */

const app = express();
const PORT = process.env.PORT || 3001;

// Trust proxy for accurate IP addresses
app.set('trust proxy', 1);

// Security middleware
app.use(securityHeaders);
app.use(generalRateLimit);
app.use(cors(corsOptions));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(sanitizeInput);

// Logging middleware
if (process.env.NODE_ENV === 'development') {
  app.use(requestLogger);
}

// Setup API documentation
setupSwagger(app);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Server is healthy',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
  });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/patients', patientRoutes);
app.use('/api/lab-results', labResultRoutes);
app.use('/api/appointments', appointmentRoutes);
app.use('/api/analytics', analyticsRoutes);
app.use('/api/notifications', notificationRoutes);
app.use('/api/assessment-sessions', assessmentSessionRoutes);
app.use('/api/health', healthRoutes);

// API documentation endpoint
app.get('/api', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Psychiatry Patient Management API',
    version: '1.0.0',
    documentation: '/api/docs',
    endpoints: {
      auth: {
        register: 'POST /api/auth/register',
        login: 'POST /api/auth/login',
        refresh: 'POST /api/auth/refresh',
        logout: 'POST /api/auth/logout',
        me: 'GET /api/auth/me',
        changePassword: 'PUT /api/auth/change-password',
      },
      patients: {
        list: 'GET /api/patients',
        create: 'POST /api/patients',
        get: 'GET /api/patients/:id',
        update: 'PUT /api/patients/:id',
        delete: 'DELETE /api/patients/:id',
        stats: 'GET /api/patients/stats',
        labResults: 'GET /api/patients/:patientId/lab-results',
        labTrends: 'GET /api/patients/:patientId/lab-results/trends',
        createAssessmentSession: 'POST /api/patients/:patientId/assessment-sessions',
        getAssessmentSessions: 'GET /api/patients/:patientId/assessment-sessions',
      },
      assessment_sessions: {
        getById: 'GET /api/assessment-sessions/:id',
      },
      labResults: {
        list: 'GET /api/lab-results',
        create: 'POST /api/lab-results',
        get: 'GET /api/lab-results/:id',
        delete: 'DELETE /api/lab-results/:id',
      },
      appointments: {
        list: 'GET /api/appointments',
        create: 'POST /api/appointments',
        get: 'GET /api/appointments/:id',
        update: 'PUT /api/appointments/:id',
        cancel: 'POST /api/appointments/:id/cancel',
        stats: 'GET /api/appointments/stats',
        availability: 'GET /api/appointments/providers/:providerId/availability',
      },
      notifications: {
        list: 'GET /api/notifications',
        create: 'POST /api/notifications',
        markRead: 'PUT /api/notifications/:id/read',
        stats: 'GET /api/notifications/stats',
        appointmentReminders: 'POST /api/notifications/appointment-reminders',
        labResult: 'POST /api/notifications/lab-result',
        processScheduled: 'POST /api/notifications/process-scheduled',
      },
      analytics: {
        dashboard: 'GET /api/analytics/dashboard',
        patients: 'GET /api/analytics/patients',
        appointments: 'GET /api/analytics/appointments',
        labResults: 'GET /api/analytics/lab-results',
        system: 'GET /api/analytics/system',
      },
    },
  });
});

// 404 handler
app.use(notFoundHandler);

// Global error handler (must be last)
app.use(errorHandler);

// Start server
const server = app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV}`);
  console.log(`🔗 API Base URL: http://localhost:${PORT}/api`);
  console.log(`🏥 Health Check: http://localhost:${PORT}/health`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});

export default app;
