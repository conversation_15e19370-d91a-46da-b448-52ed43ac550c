const axios = require('axios');

async function simpleTest() {
  try {
    console.log('Testing server...');
    
    // Test health endpoint
    const healthResponse = await axios.get('http://localhost:3002/health');
    console.log('Health check:', healthResponse.data.success ? 'OK' : 'FAILED');
    
    // Test login
    const loginResponse = await axios.post('http://localhost:3002/api/auth/login', {
      username: 'dr.smith',
      password: 'clinician123!'
    });
    
    if (loginResponse.data.success) {
      console.log('Login: OK');
      const token = loginResponse.data.data.accessToken;
      
      // Test analytics
      const analyticsResponse = await axios.get(
        'http://localhost:3002/api/analytics/lab-results?from=2024-01-01&to=2025-12-31',
        { headers: { Authorization: `Bearer ${token}` } }
      );
      
      console.log('Analytics:', analyticsResponse.data.success ? 'OK' : 'FAILED');
      if (!analyticsResponse.data.success) {
        console.log('Error:', analyticsResponse.data.error);
      }
    } else {
      console.log('Login: FAILED');
    }
    
  } catch (error) {
    console.log('Error:', error.response?.data?.error || error.message);
  }
}

simpleTest();
