"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !exports.hasOwnProperty(p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ono = void 0;
/* eslint-env commonjs */
const singleton_1 = require("./singleton");
Object.defineProperty(exports, "ono", { enumerable: true, get: function () { return singleton_1.ono; } });
var constructor_1 = require("./constructor");
Object.defineProperty(exports, "Ono", { enumerable: true, get: function () { return constructor_1.Ono; } });
__exportStar(require("./types"), exports);
exports.default = singleton_1.ono;
// CommonJS default export hack
if (typeof module === "object" && typeof module.exports === "object") {
    module.exports = Object.assign(module.exports.default, module.exports);
}
//# sourceMappingURL=index.js.map