"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.runFinalVerification = runFinalVerification;
const client_1 = require("@prisma/client");
const child_process_1 = require("child_process");
const fs_1 = __importDefault(require("fs"));
const prisma = new client_1.PrismaClient();
async function runFinalVerification() {
    console.log('🔍 Starting final verification for production readiness...\n');
    const results = [];
    console.log('📊 Verifying database integrity...');
    const dbResults = await verifyDatabase();
    results.push(dbResults);
    console.log('🔧 Verifying code quality...');
    const codeResults = await verifyCodeQuality();
    results.push(codeResults);
    console.log('🔒 Verifying security configuration...');
    const securityResults = await verifySecurity();
    results.push(securityResults);
    console.log('🌐 Verifying API endpoints...');
    const apiResults = await verifyAPI();
    results.push(apiResults);
    console.log('📚 Verifying documentation...');
    const docsResults = await verifyDocumentation();
    results.push(docsResults);
    console.log('⚡ Verifying performance...');
    const perfResults = await verifyPerformance();
    results.push(perfResults);
    generateFinalReport(results);
    await prisma.$disconnect();
}
async function verifyDatabase() {
    const checks = [];
    try {
        await prisma.$queryRaw `SELECT 1`;
        checks.push({
            name: 'Database Connection',
            status: 'PASS',
            message: 'Database connection successful'
        });
    }
    catch (error) {
        checks.push({
            name: 'Database Connection',
            status: 'FAIL',
            message: 'Database connection failed',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
    try {
        const userCount = await prisma.user.count();
        const patientCount = await prisma.patient.count();
        const labResultCount = await prisma.labResult.count();
        const appointmentCount = await prisma.appointment.count();
        checks.push({
            name: 'Database Schema',
            status: 'PASS',
            message: `All critical tables accessible`,
            details: `Users: ${userCount}, Patients: ${patientCount}, Lab Results: ${labResultCount}, Appointments: ${appointmentCount}`
        });
    }
    catch (error) {
        checks.push({
            name: 'Database Schema',
            status: 'FAIL',
            message: 'Database schema verification failed',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
    try {
        const auditCount = await prisma.auditLog.count();
        checks.push({
            name: 'Audit Logging',
            status: auditCount >= 0 ? 'PASS' : 'FAIL',
            message: `Audit log table accessible with ${auditCount} entries`
        });
    }
    catch (error) {
        checks.push({
            name: 'Audit Logging',
            status: 'FAIL',
            message: 'Audit logging verification failed'
        });
    }
    return {
        category: 'Database',
        checks
    };
}
async function verifyCodeQuality() {
    const checks = [];
    try {
        (0, child_process_1.execSync)('npx tsc --noEmit', { stdio: 'pipe' });
        checks.push({
            name: 'TypeScript Compilation',
            status: 'PASS',
            message: 'TypeScript compilation successful'
        });
    }
    catch (error) {
        checks.push({
            name: 'TypeScript Compilation',
            status: 'FAIL',
            message: 'TypeScript compilation failed',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
    const todoCount = countTODOs();
    checks.push({
        name: 'TODO Comments',
        status: todoCount === 0 ? 'PASS' : 'WARNING',
        message: `Found ${todoCount} TODO comments`,
        details: todoCount > 0 ? 'Review and resolve TODO comments before production' : undefined
    });
    const consoleLogCount = countConsoleLogs();
    checks.push({
        name: 'Console Logs',
        status: consoleLogCount === 0 ? 'PASS' : 'WARNING',
        message: `Found ${consoleLogCount} console.log statements`,
        details: consoleLogCount > 0 ? 'Remove console.log statements for production' : undefined
    });
    return {
        category: 'Code Quality',
        checks
    };
}
async function verifySecurity() {
    const checks = [];
    const requiredEnvVars = ['JWT_SECRET', 'JWT_REFRESH_SECRET', 'DATABASE_URL'];
    const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
    checks.push({
        name: 'Environment Variables',
        status: missingEnvVars.length === 0 ? 'PASS' : 'FAIL',
        message: missingEnvVars.length === 0 ? 'All required environment variables set' : `Missing: ${missingEnvVars.join(', ')}`,
        details: missingEnvVars.length > 0 ? 'Set all required environment variables' : undefined
    });
    const jwtSecret = process.env.JWT_SECRET;
    const isStrongSecret = jwtSecret && jwtSecret.length >= 32 && jwtSecret !== 'your-super-secret-jwt-key-change-in-production';
    checks.push({
        name: 'JWT Secret Strength',
        status: isStrongSecret ? 'PASS' : 'FAIL',
        message: isStrongSecret ? 'JWT secret is strong' : 'JWT secret is weak or default',
        details: !isStrongSecret ? 'Use a strong, unique JWT secret for production' : undefined
    });
    const corsOrigin = process.env.CORS_ORIGIN;
    checks.push({
        name: 'CORS Configuration',
        status: corsOrigin && corsOrigin !== '*' ? 'PASS' : 'WARNING',
        message: corsOrigin ? `CORS origin set to: ${corsOrigin}` : 'CORS origin not configured',
        details: !corsOrigin || corsOrigin === '*' ? 'Configure specific CORS origins for production' : undefined
    });
    return {
        category: 'Security',
        checks
    };
}
async function verifyAPI() {
    const checks = [];
    checks.push({
        name: 'Server Configuration',
        status: 'PASS',
        message: 'Server configuration appears valid'
    });
    const routeFiles = [
        'src/routes/auth.ts',
        'src/routes/patients.ts',
        'src/routes/labResults.ts',
        'src/routes/appointments.ts',
        'src/routes/notifications.ts',
        'src/routes/analytics.ts',
        'src/routes/health.ts'
    ];
    const missingRoutes = routeFiles.filter(file => !fs_1.default.existsSync(file));
    checks.push({
        name: 'Route Files',
        status: missingRoutes.length === 0 ? 'PASS' : 'FAIL',
        message: missingRoutes.length === 0 ? 'All route files present' : `Missing routes: ${missingRoutes.join(', ')}`
    });
    return {
        category: 'API',
        checks
    };
}
async function verifyDocumentation() {
    const checks = [];
    const docFiles = [
        'docs/database-schema.md',
        'docs/developer-setup.md',
        'README.md'
    ];
    const existingDocs = docFiles.filter(file => fs_1.default.existsSync(file));
    checks.push({
        name: 'Documentation Files',
        status: existingDocs.length === docFiles.length ? 'PASS' : 'WARNING',
        message: `${existingDocs.length}/${docFiles.length} documentation files present`,
        details: existingDocs.length < docFiles.length ? 'Complete all documentation files' : undefined
    });
    const swaggerConfigExists = fs_1.default.existsSync('src/config/swagger.ts');
    checks.push({
        name: 'API Documentation',
        status: swaggerConfigExists ? 'PASS' : 'FAIL',
        message: swaggerConfigExists ? 'Swagger configuration present' : 'Swagger configuration missing'
    });
    return {
        category: 'Documentation',
        checks
    };
}
async function verifyPerformance() {
    const checks = [];
    const memoryUsage = process.memoryUsage();
    const heapUsedMB = Math.round(memoryUsage.heapUsed / 1024 / 1024);
    checks.push({
        name: 'Memory Usage',
        status: heapUsedMB < 100 ? 'PASS' : 'WARNING',
        message: `Current heap usage: ${heapUsedMB}MB`,
        details: heapUsedMB >= 100 ? 'Monitor memory usage in production' : undefined
    });
    const cacheMiddlewareExists = fs_1.default.existsSync('src/middleware/cache.ts');
    checks.push({
        name: 'Caching Strategy',
        status: cacheMiddlewareExists ? 'PASS' : 'WARNING',
        message: cacheMiddlewareExists ? 'Caching middleware implemented' : 'No caching strategy found'
    });
    return {
        category: 'Performance',
        checks
    };
}
function countTODOs() {
    try {
        const result = (0, child_process_1.execSync)('grep -r "TODO" src/ --include="*.ts" | wc -l', { encoding: 'utf8' });
        return parseInt(result.trim()) || 0;
    }
    catch {
        return 0;
    }
}
function countConsoleLogs() {
    try {
        const result = (0, child_process_1.execSync)('grep -r "console\\.log" src/ --include="*.ts" | wc -l', { encoding: 'utf8' });
        return parseInt(result.trim()) || 0;
    }
    catch {
        return 0;
    }
}
function generateFinalReport(results) {
    console.log('\n' + '='.repeat(80));
    console.log('🎯 FINAL VERIFICATION REPORT');
    console.log('='.repeat(80));
    let totalChecks = 0;
    let passedChecks = 0;
    let failedChecks = 0;
    let warningChecks = 0;
    results.forEach(category => {
        console.log(`\n📋 ${category.category.toUpperCase()}`);
        console.log('-'.repeat(40));
        category.checks.forEach(check => {
            totalChecks++;
            const icon = check.status === 'PASS' ? '✅' : check.status === 'FAIL' ? '❌' : '⚠️';
            console.log(`${icon} ${check.name}: ${check.message}`);
            if (check.details) {
                console.log(`   📝 ${check.details}`);
            }
            if (check.status === 'PASS')
                passedChecks++;
            else if (check.status === 'FAIL')
                failedChecks++;
            else
                warningChecks++;
        });
    });
    console.log('\n' + '='.repeat(80));
    console.log('📊 SUMMARY');
    console.log('='.repeat(80));
    console.log(`Total Checks: ${totalChecks}`);
    console.log(`✅ Passed: ${passedChecks}`);
    console.log(`❌ Failed: ${failedChecks}`);
    console.log(`⚠️  Warnings: ${warningChecks}`);
    const successRate = Math.round((passedChecks / totalChecks) * 100);
    console.log(`\n🎯 Success Rate: ${successRate}%`);
    if (failedChecks === 0) {
        console.log('\n🎉 PRODUCTION READY! All critical checks passed.');
        if (warningChecks > 0) {
            console.log('⚠️  Address warnings before deployment for optimal performance.');
        }
    }
    else {
        console.log('\n🚨 NOT PRODUCTION READY! Address failed checks before deployment.');
    }
    console.log('\n📋 Next Steps:');
    console.log('1. Address any failed checks');
    console.log('2. Review and resolve warnings');
    console.log('3. Run comprehensive load testing');
    console.log('4. Set up monitoring and alerting');
    console.log('5. Configure backup and recovery procedures');
    console.log('6. Deploy to staging environment for final testing');
}
if (require.main === module) {
    runFinalVerification()
        .then(() => {
        console.log('\n✨ Verification complete!');
        process.exit(0);
    })
        .catch((error) => {
        console.error('\n💥 Verification failed:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=final-verification.js.map