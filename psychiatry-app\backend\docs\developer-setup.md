# Developer Setup Guide

## Prerequisites

### Required Software
- **Node.js**: Version 18.x or higher
- **npm**: Version 8.x or higher (comes with Node.js)
- **Git**: For version control
- **VS Code**: Recommended IDE with extensions

### Recommended VS Code Extensions
- **Prisma**: Syntax highlighting and IntelliSense for Prisma schema
- **TypeScript**: Enhanced TypeScript support
- **ESLint**: Code linting and formatting
- **Prettier**: Code formatting
- **REST Client**: For API testing
- **SQLite Viewer**: For database inspection

## Initial Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd psychiatry-app/backend
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Configuration
Create environment files from templates:

```bash
# Copy environment templates
cp .env.example .env
cp .env.test.example .env.test
```

### 4. Configure Environment Variables

#### Development Environment (`.env`)
```env
# Database
DATABASE_URL="file:./dev.db"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-in-production"
JWT_REFRESH_SECRET="your-super-secret-refresh-key-change-in-production"
JWT_EXPIRES_IN="15m"
JWT_REFRESH_EXPIRES_IN="7d"

# Server Configuration
PORT=3002
NODE_ENV="development"

# Security
BCRYPT_ROUNDS=10
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN="http://localhost:3000"

# Logging
LOG_LEVEL="debug"
```

#### Test Environment (`.env.test`)
```env
# Test Database
DATABASE_URL="file:./test.db"

# JWT Configuration (use different secrets for testing)
JWT_SECRET="test-jwt-secret"
JWT_REFRESH_SECRET="test-refresh-secret"
JWT_EXPIRES_IN="15m"
JWT_REFRESH_EXPIRES_IN="7d"

# Server Configuration
PORT=3003
NODE_ENV="test"

# Security (faster for testing)
BCRYPT_ROUNDS=4

# Logging
LOG_LEVEL="error"
```

### 5. Database Setup

#### Initialize Database
```bash
# Generate Prisma client
npm run db:generate

# Push schema to database
npm run db:push

# Optional: Open Prisma Studio to view database
npm run db:studio
```

#### Database Migrations (Production)
```bash
# Create and apply migrations
npm run db:migrate
```

### 6. Verify Installation
```bash
# Run TypeScript compilation check
npx tsc --noEmit

# Run tests
npm test

# Start development server
npm run dev
```

## Development Workflow

### 1. Starting Development
```bash
# Start the development server
npm run dev

# The server will start on http://localhost:3002
# API documentation available at http://localhost:3002/api/docs
```

### 2. Database Development

#### Schema Changes
1. Modify `prisma/schema.prisma`
2. Generate new Prisma client: `npm run db:generate`
3. Push changes to development database: `npm run db:push`
4. For production, create migration: `npm run db:migrate`

#### Database Inspection
```bash
# Open Prisma Studio (database GUI)
npm run db:studio

# View database in browser at http://localhost:5555
```

### 3. Testing

#### Run All Tests
```bash
npm test
```

#### Run Specific Test Types
```bash
# Unit tests only
npm run test:unit

# Integration tests only
npm run test:integration

# Watch mode for development
npm run test:watch

# Coverage report
npm run test:coverage
```

#### Writing Tests
- Place unit tests in `tests/unit/`
- Place integration tests in `tests/integration/`
- Use the test utilities in `tests/setup.ts`
- Follow the existing test patterns

### 4. Code Quality

#### Linting and Formatting
```bash
# Check TypeScript compilation
npx tsc --noEmit

# Run ESLint (if configured)
npx eslint src/

# Format code with Prettier (if configured)
npx prettier --write src/
```

#### Pre-commit Checklist
- [ ] TypeScript compilation passes
- [ ] All tests pass
- [ ] Code is properly formatted
- [ ] No console.log statements in production code
- [ ] Environment variables are documented

## API Development

### 1. API Documentation
- Access Swagger documentation at `http://localhost:3002/api/docs`
- Raw OpenAPI spec available at `http://localhost:3002/api/docs.json`
- Update documentation by adding JSDoc comments to routes

### 2. Adding New Endpoints

#### Step 1: Define Types
Add new types to `src/types/index.ts`:
```typescript
export interface NewEntityRequest {
  field1: string;
  field2: number;
}
```

#### Step 2: Create Service
Add business logic to `src/services/`:
```typescript
export class NewEntityService {
  static async createEntity(data: NewEntityRequest): Promise<ServiceResponse<Entity>> {
    // Implementation
  }
}
```

#### Step 3: Create Controller
Add controller to `src/controllers/`:
```typescript
export class NewEntityController {
  static async createEntity(req: Request, res: Response): Promise<void> {
    // Implementation
  }
}
```

#### Step 4: Add Routes
Add routes to `src/routes/`:
```typescript
router.post('/', authenticate, NewEntityController.createEntity);
```

#### Step 5: Add Tests
Create tests in `tests/unit/` and `tests/integration/`

### 3. Database Schema Changes

#### Adding New Model
1. Add model to `prisma/schema.prisma`
2. Run `npm run db:generate`
3. Run `npm run db:push` (development) or `npm run db:migrate` (production)
4. Update TypeScript types if needed
5. Add service methods
6. Add controller methods
7. Add routes
8. Add tests

#### Modifying Existing Model
1. Update `prisma/schema.prisma`
2. Create migration: `npm run db:migrate`
3. Update related TypeScript types
4. Update service methods
5. Update tests

## Debugging

### 1. Development Debugging
- Use `console.log()` for simple debugging
- Use VS Code debugger for complex issues
- Check server logs for error details
- Use Prisma Studio to inspect database state

### 2. Database Debugging
```bash
# View database schema
npx prisma db pull

# Reset database (WARNING: deletes all data)
npx prisma db push --force-reset

# View generated SQL
npx prisma db push --preview-feature
```

### 3. Common Issues

#### Database Connection Issues
- Check `DATABASE_URL` in `.env`
- Ensure database file permissions are correct
- Verify Prisma client is generated: `npm run db:generate`

#### TypeScript Errors
- Run `npx tsc --noEmit` to see all errors
- Check import paths use `@/` alias correctly
- Ensure all types are properly exported/imported

#### Test Failures
- Check test database is properly set up
- Verify test environment variables in `.env.test`
- Ensure test isolation (each test should clean up)

## Production Deployment

### 1. Environment Setup
- Use PostgreSQL instead of SQLite
- Set strong JWT secrets
- Configure proper CORS origins
- Set appropriate rate limits
- Enable production logging

### 2. Database Migration
```bash
# Run migrations in production
npm run db:migrate

# Generate Prisma client
npm run db:generate
```

### 3. Build and Start
```bash
# Build TypeScript
npm run build

# Start production server
npm start
```

### 4. Health Checks
- Monitor `/health` endpoint
- Check database connectivity
- Monitor error logs
- Verify API documentation is accessible

## Security Considerations

### 1. Environment Variables
- Never commit `.env` files to version control
- Use strong, unique secrets for JWT
- Rotate secrets regularly in production
- Use environment-specific configurations

### 2. Database Security
- Use strong database passwords
- Enable database encryption
- Regular security updates
- Monitor access logs

### 3. API Security
- Implement proper authentication
- Use HTTPS in production
- Validate all input data
- Implement rate limiting
- Monitor for suspicious activity

## Support and Resources

### Documentation
- [Prisma Documentation](https://www.prisma.io/docs/)
- [Express.js Documentation](https://expressjs.com/)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
- [Jest Testing Framework](https://jestjs.io/docs/getting-started)

### Getting Help
- Check existing issues in the repository
- Review API documentation at `/api/docs`
- Consult the database schema documentation
- Ask team members for guidance

### Contributing
- Follow the established code patterns
- Write tests for new functionality
- Update documentation for API changes
- Follow the commit message conventions
