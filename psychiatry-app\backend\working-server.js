const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const dotenv = require('dotenv');
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

// Load environment variables
dotenv.config();

console.log('🚀 Starting Psychiatry App Server...');

const app = express();
const PORT = process.env.PORT || 3002;
const prisma = new PrismaClient();

// Basic middleware
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:3000'],
  credentials: true
}));

// Add raw body parsing for debugging
app.use((req, res, next) => {
  let data = '';
  req.setEncoding('utf8');
  req.on('data', chunk => {
    data += chunk;
  });
  req.on('end', () => {
    if (data) {
      console.log('Raw request body:', data);
      try {
        req.body = JSON.parse(data);
      } catch (e) {
        console.error('JSON parse error:', e);
        return res.status(400).json({
          success: false,
          message: 'Invalid JSON in request body',
          error: e.message,
          receivedData: data
        });
      }
    }
    next();
  });
});

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging middleware
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
}

// Request logging middleware
app.use((req, res, next) => {
  console.log(`${req.method} ${req.path} - Content-Type: ${req.headers['content-type']}`);
  if (req.body && Object.keys(req.body).length > 0) {
    console.log('Request body:', JSON.stringify(req.body, null, 2));
  }
  next();
});

// Health check endpoint
app.get('/health', async (req, res) => {
  try {
    // Test database connection
    await prisma.$queryRaw`SELECT 1`;
    
    res.status(200).json({
      success: true,
      message: 'Server is healthy',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      database: 'connected',
      version: '1.0.0'
    });
  } catch (error) {
    console.error('Health check failed:', error);
    res.status(500).json({
      success: false,
      message: 'Database connection failed',
      error: error.message
    });
  }
});

// API info endpoint
app.get('/api', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Psychiatry Patient Management API',
    version: '1.0.0',
    status: 'running',
    features: [
      'User Management',
      'Patient Management', 
      'Lab Results Tracking',
      'Appointment Scheduling',
      'Notification System',
      'Recurring Appointments',
      'Audit Logging'
    ],
    endpoints: {
      health: 'GET /health',
      api: 'GET /api',
      stats: 'GET /api/stats',
      users: {
        list: 'GET /api/users',
        create: 'POST /api/users',
        get: 'GET /api/users/:id'
      },
      patients: {
        list: 'GET /api/patients',
        create: 'POST /api/patients',
        get: 'GET /api/patients/:id'
      },
      labResults: {
        list: 'GET /api/lab-results',
        create: 'POST /api/lab-results'
      },
      appointments: {
        list: 'GET /api/appointments',
        create: 'POST /api/appointments'
      }
    }
  });
});

// Database stats endpoint
app.get('/api/stats', async (req, res) => {
  try {
    const stats = {
      users: await prisma.user.count(),
      patients: await prisma.patient.count(),
      labResults: await prisma.labResult.count(),
      appointments: await prisma.appointment.count(),
      notifications: await prisma.notification.count(),
      recurringAppointments: await prisma.recurringAppointment.count(),
      auditLogs: await prisma.auditLog.count()
    };

    res.status(200).json({
      success: true,
      message: 'Database statistics',
      data: stats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Stats query failed:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch statistics',
      error: error.message
    });
  }
});

// Authentication endpoints
app.post('/api/auth/login', async (req, res) => {
  try {
    console.log('Login request body:', req.body);
    const { email, password, username } = req.body;

    if ((!email && !username) || !password) {
      console.log('Missing required fields:', { email, username, password });
      return res.status(400).json({
        success: false,
        message: 'Email/username and password are required'
      });
    }

    // Find user by email or username
    let user;
    try {
      if (email) {
        console.log('Looking up user by email:', email);
        user = await prisma.user.findUnique({
          where: { email },
          select: {
            id: true,
            username: true,
            email: true,
            password: true,
            firstName: true,
            lastName: true,
            role: true,
            isActive: true
          }
        });
        console.log('User found by email:', user ? 'Yes' : 'No');
      } else if (username) {
        console.log('Looking up user by username:', username);
        user = await prisma.user.findUnique({
          where: { username },
          select: {
            id: true,
            username: true,
            email: true,
            password: true,
            firstName: true,
            lastName: true,
            role: true,
            isActive: true
          }
        });
        console.log('User found by username:', user ? 'Yes' : 'No');
      }
    } catch (lookupError) {
      console.error('Error looking up user:', lookupError);
      throw lookupError;
    }

    if (!user || !user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials or account disabled'
      });
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Update last login
    await prisma.user.update({
      where: { id: user.id },
      data: { lastLogin: new Date() }
    });

    // Create mock tokens (in production, use proper JWT)
    const accessToken = Buffer.from(JSON.stringify({
      userId: user.id,
      email: user.email,
      role: user.role,
      exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1 hour
    })).toString('base64');

    const refreshToken = Buffer.from(JSON.stringify({
      userId: user.id,
      type: 'refresh',
      exp: Math.floor(Date.now() / 1000) + (60 * 60 * 24 * 7) // 7 days
    })).toString('base64');

    const { password: _, ...userWithoutPassword } = user;

    res.status(200).json({
      success: true,
      message: 'Login successful',
      data: {
        user: userWithoutPassword,
        accessToken,
        refreshToken
      }
    });
  } catch (error) {
    console.error('Login failed:', error);
    res.status(500).json({
      success: false,
      message: 'Login failed',
      error: error.message
    });
  }
});

app.post('/api/auth/logout', async (req, res) => {
  try {
    // In a real implementation, you would invalidate the refresh token
    res.status(200).json({
      success: true,
      message: 'Logout successful'
    });
  } catch (error) {
    console.error('Logout failed:', error);
    res.status(500).json({
      success: false,
      message: 'Logout failed',
      error: error.message
    });
  }
});

app.post('/api/auth/refresh', async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({
        success: false,
        message: 'Refresh token is required'
      });
    }

    try {
      // Decode refresh token (in production, verify JWT signature)
      const tokenData = JSON.parse(Buffer.from(refreshToken, 'base64').toString());

      if (tokenData.exp < Math.floor(Date.now() / 1000)) {
        return res.status(401).json({
          success: false,
          message: 'Refresh token expired'
        });
      }

      // Get user
      const user = await prisma.user.findUnique({
        where: { id: tokenData.userId },
        select: {
          id: true,
          username: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          isActive: true
        }
      });

      if (!user || !user.isActive) {
        return res.status(401).json({
          success: false,
          message: 'User not found or disabled'
        });
      }

      // Create new tokens
      const newAccessToken = Buffer.from(JSON.stringify({
        userId: user.id,
        email: user.email,
        role: user.role,
        exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1 hour
      })).toString('base64');

      const newRefreshToken = Buffer.from(JSON.stringify({
        userId: user.id,
        type: 'refresh',
        exp: Math.floor(Date.now() / 1000) + (60 * 60 * 24 * 7) // 7 days
      })).toString('base64');

      res.status(200).json({
        success: true,
        message: 'Token refreshed',
        data: {
          user,
          accessToken: newAccessToken,
          refreshToken: newRefreshToken
        }
      });
    } catch (decodeError) {
      return res.status(401).json({
        success: false,
        message: 'Invalid refresh token'
      });
    }
  } catch (error) {
    console.error('Token refresh failed:', error);
    res.status(500).json({
      success: false,
      message: 'Token refresh failed',
      error: error.message
    });
  }
});

app.get('/api/auth/me', async (req, res) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'Authorization header required'
      });
    }

    const token = authHeader.substring(7);

    try {
      // Decode token (in production, verify JWT signature)
      const tokenData = JSON.parse(Buffer.from(token, 'base64').toString());

      if (tokenData.exp < Math.floor(Date.now() / 1000)) {
        return res.status(401).json({
          success: false,
          message: 'Token expired'
        });
      }

      // Get user
      const user = await prisma.user.findUnique({
        where: { id: tokenData.userId },
        select: {
          id: true,
          username: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          isActive: true,
          lastLogin: true
        }
      });

      if (!user || !user.isActive) {
        return res.status(401).json({
          success: false,
          message: 'User not found or disabled'
        });
      }

      res.status(200).json({
        success: true,
        data: user
      });
    } catch (decodeError) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token'
      });
    }
  } catch (error) {
    console.error('Get current user failed:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get current user',
      error: error.message
    });
  }
});

// User endpoints
app.get('/api/users', async (req, res) => {
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        createdAt: true,
        lastLogin: true
      },
      orderBy: { createdAt: 'desc' }
    });
    
    res.status(200).json({
      success: true,
      data: users,
      count: users.length
    });
  } catch (error) {
    console.error('Failed to fetch users:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch users',
      error: error.message
    });
  }
});

app.post('/api/users', async (req, res) => {
  try {
    const { username, email, password, firstName, lastName, role = 'CLINICIAN' } = req.body;
    
    if (!username || !email || !password || !firstName || !lastName) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: username, email, password, firstName, lastName'
      });
    }
    
    // Check if user already exists
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { username },
          { email }
        ]
      }
    });
    
    if (existingUser) {
      return res.status(409).json({
        success: false,
        message: 'User with this username or email already exists'
      });
    }
    
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);
    
    const user = await prisma.user.create({
      data: {
        username,
        email,
        password: hashedPassword,
        firstName,
        lastName,
        role
      },
      select: {
        id: true,
        username: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        createdAt: true
      }
    });
    
    res.status(201).json({
      success: true,
      data: user,
      message: 'User created successfully'
    });
  } catch (error) {
    console.error('Failed to create user:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create user',
      error: error.message
    });
  }
});

app.get('/api/users/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        username: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        createdAt: true,
        lastLogin: true,
        _count: {
          select: {
            createdPatients: true,
            auditLogs: true
          }
        }
      }
    });
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }
    
    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Failed to fetch user:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user',
      error: error.message
    });
  }
});

// Patient endpoints
app.get('/api/patients', async (req, res) => {
  try {
    const patients = await prisma.patient.findMany({
      where: {
        isDeleted: false
      },
      select: {
        id: true,
        patientId: true,
        firstName: true,
        lastName: true,
        dateOfBirth: true,
        gender: true,
        phone: true,
        email: true,
        isActive: true,
        createdAt: true,
        _count: {
          select: {
            labResults: true,
            appointments: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });
    
    res.status(200).json({
      success: true,
      data: patients,
      count: patients.length
    });
  } catch (error) {
    console.error('Failed to fetch patients:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch patients',
      error: error.message
    });
  }
});

app.post('/api/patients', async (req, res) => {
  try {
    const {
      firstName,
      lastName,
      dateOfBirth,
      gender,
      phone,
      email,
      createdBy
    } = req.body;
    
    if (!firstName || !lastName || !dateOfBirth || !gender) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: firstName, lastName, dateOfBirth, gender'
      });
    }

    // Get a default user if createdBy is not provided
    let finalCreatedBy = createdBy;
    if (!finalCreatedBy) {
      const defaultUser = await prisma.user.findFirst({
        where: { role: 'ADMIN' },
        select: { id: true }
      });
      if (!defaultUser) {
        return res.status(500).json({
          success: false,
          message: 'No admin user found to assign as creator'
        });
      }
      finalCreatedBy = defaultUser.id;
    }
    
    // Generate patient ID
    const patientCount = await prisma.patient.count();
    const patientId = `P-${new Date().getFullYear()}-${String(patientCount + 1).padStart(3, '0')}`;
    
    const patient = await prisma.patient.create({
      data: {
        patientId,
        firstName,
        lastName,
        dateOfBirth: new Date(dateOfBirth),
        gender,
        phone: phone || null,
        email: email || null,
        createdBy: finalCreatedBy
      },
      select: {
        id: true,
        patientId: true,
        firstName: true,
        lastName: true,
        dateOfBirth: true,
        gender: true,
        phone: true,
        email: true,
        isActive: true,
        createdAt: true
      }
    });
    
    res.status(201).json({
      success: true,
      data: patient,
      message: 'Patient created successfully'
    });
  } catch (error) {
    console.error('Failed to create patient:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create patient',
      error: error.message
    });
  }
});

// Get patient by ID
app.get('/api/patients/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Look up the patient by either formatted patientId or UUID
    const patient = await prisma.patient.findFirst({
      where: {
        OR: [
          { patientId: id },
          { id: id }
        ],
        isDeleted: false
      },
      include: {
        labResults: {
          where: { isDeleted: false },
          orderBy: { testDate: 'desc' },
          take: 5
        },
        appointments: {
          where: { isDeleted: false },
          orderBy: { date: 'desc' },
          take: 5
        }
      }
    });

    if (!patient) {
      return res.status(404).json({
        success: false,
        message: 'Patient not found',
        error: `No patient found with ID: ${id}`
      });
    }

    res.status(200).json({
      success: true,
      data: { patient },
      message: 'Patient retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching patient:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch patient',
      error: error.message
    });
  }
});

// Lab Results endpoints
app.get('/api/lab-results', async (req, res) => {
  try {
    const { patientId, testType, limit = 50 } = req.query;

    const where = {
      isDeleted: false
    };

    if (patientId) {
      where.patientId = patientId;
    }

    if (testType) {
      where.testType = testType;
    }

    const labResults = await prisma.labResult.findMany({
      where,
      include: {
        patient: {
          select: {
            id: true,
            patientId: true,
            firstName: true,
            lastName: true
          }
        }
      },
      orderBy: {
        testDate: 'desc'
      },
      take: parseInt(limit)
    });

    // Parse JSON fields
    const processedResults = labResults.map(result => ({
      ...result,
      results: typeof result.results === 'string' ? JSON.parse(result.results) : result.results,
      normalRanges: result.normalRanges ? (typeof result.normalRanges === 'string' ? JSON.parse(result.normalRanges) : result.normalRanges) : null,
      flags: result.flags ? (typeof result.flags === 'string' ? JSON.parse(result.flags) : result.flags) : null
    }));

    res.status(200).json({
      success: true,
      data: processedResults,
      count: processedResults.length,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error fetching lab results:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch lab results',
      error: error.message
    });
  }
});

app.post('/api/lab-results', async (req, res) => {
  try {
    const {
      patientId,
      testType,
      testDate,
      orderedBy,
      labName,
      results,
      normalRanges,
      flags,
      notes,
      status = 'COMPLETED'
    } = req.body;

    // Validate required fields
    if (!patientId || !testType || !testDate || !orderedBy || !results) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: patientId, testType, testDate, orderedBy, results'
      });
    }

    // Verify patient exists - look up by either UUID id or human-readable patientId
    const patient = await prisma.patient.findFirst({
      where: {
        OR: [
          { id: patientId },
          { patientId: patientId }
        ],
        isDeleted: false
      }
    });

    if (!patient) {
      return res.status(404).json({
        success: false,
        message: 'Patient not found'
      });
    }

    // Get a default user for createdBy (in a real app, this would come from authentication)
    const defaultUser = await prisma.user.findFirst();
    if (!defaultUser) {
      return res.status(500).json({
        success: false,
        message: 'No users found in system'
      });
    }

    // Create lab result
    const labResult = await prisma.labResult.create({
      data: {
        patientId: patient.id, // Use the patient's UUID, not the input patientId
        testType,
        testDate: new Date(testDate),
        orderedBy: orderedBy.trim(),
        labName: labName?.trim() || null,
        results: typeof results === 'string' ? results : JSON.stringify(results),
        normalRanges: normalRanges ? (typeof normalRanges === 'string' ? normalRanges : JSON.stringify(normalRanges)) : null,
        flags: flags ? (typeof flags === 'string' ? flags : JSON.stringify(flags)) : null,
        notes: notes?.trim() || null,
        status,
        createdBy: defaultUser.id
      },
      include: {
        patient: {
          select: {
            id: true,
            patientId: true,
            firstName: true,
            lastName: true
          }
        }
      }
    });

    // Parse JSON fields for response
    const processedResult = {
      ...labResult,
      results: typeof labResult.results === 'string' ? JSON.parse(labResult.results) : labResult.results,
      normalRanges: labResult.normalRanges ? (typeof labResult.normalRanges === 'string' ? JSON.parse(labResult.normalRanges) : labResult.normalRanges) : null,
      flags: labResult.flags ? (typeof labResult.flags === 'string' ? JSON.parse(labResult.flags) : labResult.flags) : null
    };

    res.status(201).json({
      success: true,
      message: 'Lab result created successfully',
      data: processedResult,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error creating lab result:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create lab result',
      error: error.message
    });
  }
});

// Appointments endpoints
app.get('/api/appointments', async (req, res) => {
  try {
    const appointments = await prisma.appointment.findMany({
      where: {
        isDeleted: false
      },
      include: {
        patient: {
          select: {
            id: true,
            patientId: true,
            firstName: true,
            lastName: true
          }
        }
      },
      orderBy: { date: 'desc' }
    });

    res.status(200).json({
      success: true,
      data: appointments,
      count: appointments.length
    });
  } catch (error) {
    console.error('Failed to fetch appointments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch appointments',
      error: error.message
    });
  }
});

app.post('/api/appointments', async (req, res) => {
  try {
    const {
      patientId,
      providerId,
      date,
      duration = 60,
      type = 'CONSULTATION',
      notes
    } = req.body;

    if (!patientId || !date) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: patientId, date'
      });
    }

    // Verify patient exists
    const patient = await prisma.patient.findUnique({
      where: { id: patientId }
    });

    if (!patient) {
      return res.status(404).json({
        success: false,
        message: 'Patient not found'
      });
    }
    
    // Fix date parsing
    let parsedDate;
    if (date) {
      // Handle various date formats
      parsedDate = new Date(date.replace(/ AM| PM/g, ''));
      if (isNaN(parsedDate.getTime())) {
        // Try alternative parsing
        parsedDate = new Date(date);
      }
    }
    
    if (!parsedDate || isNaN(parsedDate.getTime())) {
      return res.status(400).json({
        success: false,
        message: 'Invalid date format'
      });
    }

    const appointment = await prisma.appointment.create({
      data: {
        patientId,
        providerId,
        date: parsedDate,
        duration: parseInt(duration),
        type,
        notes: notes?.trim() || null,
        status: 'SCHEDULED'
      },
      include: {
        patient: {
          select: {
            id: true,
            patientId: true,
            firstName: true,
            lastName: true
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      data: appointment,
      message: 'Appointment created successfully'
    });
  } catch (error) {
    console.error('Failed to create appointment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create appointment',
      error: error.message
    });
  }
});

// Analytics endpoints
app.get('/api/analytics/dashboard', async (req, res) => {
  try {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    const [
      totalPatients,
      newPatientsThisWeek,
      newPatientsThisMonth,
      totalAppointments,
      todayAppointments,
      thisWeekAppointments,
      upcomingAppointments,
      totalLabResults,
      pendingLabResults,
      flaggedLabResults,
      totalUsers,
      activeUsers
    ] = await Promise.all([
      // Patients
      prisma.patient.count({ where: { isDeleted: false } }),
      prisma.patient.count({
        where: {
          isDeleted: false,
          createdAt: { gte: thisWeek }
        }
      }),
      prisma.patient.count({
        where: {
          isDeleted: false,
          createdAt: { gte: thisMonth }
        }
      }),

      // Appointments
      prisma.appointment.count({ where: { isDeleted: false } }),
      prisma.appointment.count({
        where: {
          isDeleted: false,
          date: { gte: today, lt: new Date(today.getTime() + 24 * 60 * 60 * 1000) },
          status: { in: ['SCHEDULED', 'CONFIRMED', 'IN_PROGRESS'] }
        }
      }),
      prisma.appointment.count({
        where: {
          isDeleted: false,
          date: { gte: thisWeek }
        }
      }),
      prisma.appointment.count({
        where: {
          isDeleted: false,
          date: { gte: now },
          status: { in: ['SCHEDULED', 'CONFIRMED'] }
        }
      }),

      // Lab Results
      prisma.labResult.count({ where: { isDeleted: false } }),
      prisma.labResult.count({
        where: {
          isDeleted: false,
          status: { in: ['PENDING', 'IN_PROGRESS'] }
        }
      }),
      prisma.labResult.count({
        where: {
          isDeleted: false,
          flags: { not: null }
        }
      }),

      // Users
      prisma.user.count(),
      prisma.user.count({ where: { isActive: true } })
    ]);

    const analytics = {
      patients: {
        total: totalPatients,
        newThisWeek: newPatientsThisWeek,
        newThisMonth: newPatientsThisMonth,
        growthRate: totalPatients > 0 ? (newPatientsThisMonth / totalPatients) * 100 : 0
      },
      appointments: {
        total: totalAppointments,
        today: todayAppointments,
        thisWeek: thisWeekAppointments,
        upcoming: upcomingAppointments
      },
      labResults: {
        total: totalLabResults,
        pending: pendingLabResults,
        flagged: flaggedLabResults,
        flaggedPercentage: totalLabResults > 0 ? (flaggedLabResults / totalLabResults) * 100 : 0
      },
      system: {
        totalUsers,
        activeUsers,
        activePercentage: totalUsers > 0 ? (activeUsers / totalUsers) * 100 : 0
      }
    };

    res.status(200).json({
      success: true,
      data: { analytics },
      message: 'Dashboard analytics retrieved successfully'
    });
  } catch (error) {
    console.error('Failed to fetch dashboard analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard analytics',
      error: error.message
    });
  }
});

app.get('/api/analytics/system', async (req, res) => {
  try {
    const { from, to } = req.query;
    
    if (!from || !to) {
      return res.status(400).json({
        success: false,
        message: 'from and to date parameters are required'
      });
    }

    const fromDate = new Date(from);
    const toDate = new Date(to);

    // Get system analytics data
    const [
      totalUsers,
      activeUsers,
      totalSessions,
      systemUptime
    ] = await Promise.all([
      // Total users
      prisma.user.count(),
      // Active users (logged in within date range)
      prisma.user.count({
        where: {
          lastLogin: { gte: fromDate, lte: toDate }
        }
      }),
      // Total assessment sessions in range
      prisma.patientAssessment.count({
        where: {
          isDeleted: false,
          sessionDate: { gte: fromDate, lte: toDate }
        }
      }),
      // System uptime (mock data)
      Promise.resolve(process.uptime())
    ]);

    res.status(200).json({
      success: true,
      data: {
        totalUsers,
        activeUsers,
        totalSessions,
        systemUptime: Math.floor(systemUptime),
        memoryUsage: process.memoryUsage(),
        version: '1.0.0'
      },
      message: 'System analytics retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching system analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch system analytics',
      error: error.message
    });
  }
});

// Export endpoints
app.get('/api/export/patients', async (req, res) => {
  try {
    const patients = await prisma.patient.findMany({
      where: { isDeleted: false },
      select: {
        patientId: true,
        firstName: true,
        lastName: true,
        dateOfBirth: true,
        gender: true,
        phone: true,
        email: true,
        isActive: true,
        createdAt: true
      }
    });

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename=patients.csv');

    const csvHeader = 'Patient ID,First Name,Last Name,Date of Birth,Gender,Phone,Email,Status,Created At\n';
    const csvData = patients.map(p =>
      `${p.patientId},"${p.firstName}","${p.lastName}",${p.dateOfBirth.toISOString().split('T')[0]},${p.gender},"${p.phone || ''}","${p.email || ''}",${p.isActive ? 'Active' : 'Inactive'},${p.createdAt.toISOString()}`
    ).join('\n');

    res.send(csvHeader + csvData);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to export patients',
      error: error.message
    });
  }
});

app.get('/api/export/lab-results', async (req, res) => {
  try {
    const labResults = await prisma.labResult.findMany({
      include: {
        patient: {
          select: {
            patientId: true,
            firstName: true,
            lastName: true
          }
        }
      },
      orderBy: { testDate: 'desc' }
    });

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename=lab-results.csv');

    const csvHeader = 'Patient ID,Patient Name,Test Type,Test Date,Ordered By,Lab Name,Status,Notes,Created At\n';
    const csvData = labResults.map(lr =>
      `${lr.patient.patientId},"${lr.patient.firstName} ${lr.patient.lastName}",${lr.testType},${lr.testDate.toISOString().split('T')[0]},"${lr.orderedBy}","${lr.labName || ''}",${lr.status},"${lr.notes || ''}",${lr.createdAt.toISOString()}`
    ).join('\n');

    res.send(csvHeader + csvData);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to export lab results',
      error: error.message
    });
  }
});

app.get('/api/export/appointments', async (req, res) => {
  try {
    const appointments = await prisma.appointment.findMany({
      include: {
        patient: {
          select: {
            patientId: true,
            firstName: true,
            lastName: true
          }
        }
      },
      orderBy: { date: 'asc' }
    });

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename=appointments.csv');

    const csvHeader = 'Patient ID,Patient Name,Date,Time,Duration (min),Type,Status,Notes,Created At\n';
    const csvData = appointments.map(a =>
      `${a.patient.patientId},"${a.patient.firstName} ${a.patient.lastName}",${a.date.toISOString().split('T')[0]},${a.date.toISOString().split('T')[1].substring(0,5)},${a.duration},${a.type},${a.status},"${a.notes || ''}",${a.createdAt.toISOString()}`
    ).join('\n');

    res.send(csvHeader + csvData);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to export appointments',
      error: error.message
    });
  }
});

app.get('/api/export/users', async (req, res) => {
  try {
    const users = await prisma.user.findMany({
      select: {
        username: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        createdAt: true
      }
    });

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename=users.csv');

    const csvHeader = 'Username,Email,First Name,Last Name,Role,Status,Created At\n';
    const csvData = users.map(u =>
      `"${u.username}","${u.email}","${u.firstName}","${u.lastName}",${u.role},${u.isActive ? 'Active' : 'Inactive'},${u.createdAt.toISOString()}`
    ).join('\n');

    res.send(csvHeader + csvData);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to export users',
      error: error.message
    });
  }
});

// Assessment session endpoints
app.post('/api/patients/:patientId/assessment-sessions', async (req, res) => {
  try {
    console.log('Assessment session creation request:', {
      patientId: req.params.patientId,
      body: req.body,
      headers: req.headers
    });

    const { patientId } = req.params;
    const assessmentData = req.body;

    // Validate patient exists - try to find by both id and patientId
    const patient = await prisma.patient.findFirst({
      where: {
        OR: [
          { id: patientId },
          { patientId: patientId }
        ]
      }
    });

    console.log('Patient lookup result:', patient ? 'Found' : 'Not found');

    if (!patient) {
      return res.status(404).json({
        success: false,
        message: 'Patient not found'
      });
    }

    // Get the admin user for now (in production, this would come from auth middleware)
    const adminUser = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    });

    console.log('Admin user lookup result:', adminUser ? 'Found' : 'Not found');

    if (!adminUser) {
      return res.status(500).json({
        success: false,
        message: 'No admin user found in the system'
      });
    }

    console.log('Creating assessment session with data:', {
      patientId: patient.id, // Use the actual patient ID
      assessorId: adminUser.id,
      sessionDate: new Date(assessmentData.sessionDate || new Date()),
      status: assessmentData.status || 'completed',
      duration: assessmentData.duration || 60,
    });

    // Create assessment session
    const result = await prisma.patientAssessment.create({
      data: {
        patientId: patient.id, // Use the actual patient ID
        assessorId: adminUser.id,
        sessionDate: new Date(assessmentData.sessionDate || new Date()),
        assessmentData: JSON.stringify(assessmentData),
        status: assessmentData.status || 'completed',
        duration: assessmentData.duration || 60,
      },
    });

    console.log('Assessment session created successfully:', result.id);

    res.status(201).json({
      success: true,
      data: {
        id: result.id,
        ...assessmentData,
        assessorId: result.assessorId,
      },
      message: 'Assessment session created successfully',
    });
  } catch (error) {
    console.error('Error creating assessment session:', error);
    console.error('Error stack:', error.stack);
    res.status(500).json({
      success: false,
      message: 'Failed to create assessment session',
      error: error.message
    });
  }
});

app.get('/api/patients/:patientId/assessment-sessions', async (req, res) => {
  try {
    const { patientId } = req.params;

    const assessments = await prisma.patientAssessment.findMany({
      where: {
        patientId,
        isDeleted: false,
      },
      orderBy: {
        sessionDate: 'desc',
      },
    });

    // Parse the JSON data for each assessment
    const parsedAssessments = assessments.map(assessment => ({
      id: assessment.id,
      ...JSON.parse(assessment.assessmentData),
      assessorId: assessment.assessorId,
      createdAt: assessment.createdAt,
      updatedAt: assessment.updatedAt,
    }));

    res.status(200).json({
      success: true,
      data: parsedAssessments,
      message: 'Assessment sessions retrieved successfully',
    });
  } catch (error) {
    console.error('Error retrieving assessment sessions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve assessment sessions',
      error: error.message
    });
  }
});

app.get('/api/assessment-sessions/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const assessment = await prisma.patientAssessment.findUnique({
      where: {
        id,
        isDeleted: false,
      },
    });

    if (!assessment) {
      return res.status(404).json({
        success: false,
        error: 'Assessment session not found',
      });
    }

    // Parse the JSON data
    const parsedAssessment = {
      id: assessment.id,
      ...JSON.parse(assessment.assessmentData),
      assessorId: assessment.assessorId,
      createdAt: assessment.createdAt,
      updatedAt: assessment.updatedAt,
    };

    res.status(200).json({
      success: true,
      data: parsedAssessment,
      message: 'Assessment session retrieved successfully',
    });
  } catch (error) {
    console.error('Error retrieving assessment session:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve assessment session',
      error: error.message
    });
  }
});

// Analytics endpoints
app.get('/api/analytics/patients', async (req, res) => {
  try {
    const { from, to } = req.query;

    if (!from || !to) {
      return res.status(400).json({
        success: false,
        message: 'from and to date parameters are required'
      });
    }

    const fromDate = new Date(from);
    const toDate = new Date(to);

    // Get patient analytics data
    const [
      totalPatients,
      genderDistribution,
      ageGroups,
      patientsWithAppointments,
      patientsWithLabResults,
      registrationTrend
    ] = await Promise.all([
      // Total patients in date range
      prisma.patient.count({
        where: {
          isDeleted: false,
          createdAt: { gte: fromDate, lte: toDate }
        }
      }),
      // Gender distribution
      prisma.patient.groupBy({
        by: ['gender'],
        where: {
          isDeleted: false,
          createdAt: { gte: fromDate, lte: toDate }
        },
        _count: { gender: true }
      }),
      // Age groups (simplified for SQLite)
      prisma.$queryRaw`
        SELECT
          CASE
            WHEN (strftime('%Y', 'now') - strftime('%Y', dateOfBirth)) < 18 THEN 'Under 18'
            WHEN (strftime('%Y', 'now') - strftime('%Y', dateOfBirth)) BETWEEN 18 AND 30 THEN '18-30'
            WHEN (strftime('%Y', 'now') - strftime('%Y', dateOfBirth)) BETWEEN 31 AND 50 THEN '31-50'
            WHEN (strftime('%Y', 'now') - strftime('%Y', dateOfBirth)) BETWEEN 51 AND 70 THEN '51-70'
            ELSE 'Over 70'
          END as age_group,
          COUNT(*) as count
        FROM patients
        WHERE isDeleted = false
          AND createdAt >= ${fromDate}
          AND createdAt <= ${toDate}
        GROUP BY age_group
        ORDER BY age_group
      `,
      // Patients with appointments
      prisma.patient.count({
        where: {
          isDeleted: false,
          createdAt: { gte: fromDate, lte: toDate },
          appointments: { some: { isDeleted: false } }
        }
      }),
      // Patients with lab results
      prisma.patient.count({
        where: {
          isDeleted: false,
          createdAt: { gte: fromDate, lte: toDate },
          labResults: { some: { isDeleted: false } }
        }
      }),
      // Registration trend (daily)
      prisma.$queryRaw`
        SELECT
          DATE(createdAt) as date,
          COUNT(*) as count
        FROM patients
        WHERE isDeleted = false
          AND createdAt >= ${fromDate}
          AND createdAt <= ${toDate}
        GROUP BY DATE(createdAt)
        ORDER BY date
      `
    ]);

    const analytics = {
      demographics: {
        gender: genderDistribution.reduce((acc, item) => {
          acc[item.gender] = item._count.gender;
          return acc;
        }, {}),
        ageGroups: ageGroups.map(item => ({
          age_group: item.age_group,
          count: Number(item.count)
        }))
      },
      trends: {
        registration: registrationTrend.map(item => ({
          date: item.date,
          count: Number(item.count)
        }))
      },
      engagement: {
        totalPatients,
        withAppointments: patientsWithAppointments,
        withLabResults: patientsWithLabResults,
        appointmentRate: totalPatients > 0 ? (patientsWithAppointments / totalPatients) * 100 : 0,
        labResultRate: totalPatients > 0 ? (patientsWithLabResults / totalPatients) * 100 : 0
      }
    };

    res.status(200).json({
      success: true,
      data: { analytics },
      message: 'Patient analytics retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching patient analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch patient analytics',
      error: error.message
    });
  }
});

app.get('/api/analytics/appointments', async (req, res) => {
  try {
    const { from, to } = req.query;

    if (!from || !to) {
      return res.status(400).json({
        success: false,
        message: 'from and to date parameters are required'
      });
    }

    const fromDate = new Date(from);
    const toDate = new Date(to);

    // Get appointment analytics data
    const [
      totalAppointments,
      statusDistribution,
      typeDistribution,
      dailyTrend,
      durationStats
    ] = await Promise.all([
      // Total appointments in date range
      prisma.appointment.count({
        where: {
          isDeleted: false,
          date: { gte: fromDate, lte: toDate }
        }
      }),
      // Status distribution
      prisma.appointment.groupBy({
        by: ['status'],
        where: {
          isDeleted: false,
          date: { gte: fromDate, lte: toDate }
        },
        _count: { status: true }
      }),
      // Type distribution
      prisma.appointment.groupBy({
        by: ['type'],
        where: {
          isDeleted: false,
          date: { gte: fromDate, lte: toDate }
        },
        _count: { type: true }
      }),
      // Daily trend
      prisma.$queryRaw`
        SELECT
          DATE(date) as day,
          COUNT(*) as count
        FROM appointments
        WHERE isDeleted = false
          AND date >= ${fromDate}
          AND date <= ${toDate}
        GROUP BY DATE(date)
        ORDER BY day
      `,
      // Duration statistics
      prisma.appointment.aggregate({
        where: {
          isDeleted: false,
          date: { gte: fromDate, lte: toDate },
          status: 'COMPLETED'
        },
        _avg: { duration: true },
        _min: { duration: true },
        _max: { duration: true }
      })
    ]);

    const analytics = {
      overview: {
        total: totalAppointments,
        statusDistribution: statusDistribution.reduce((acc, item) => {
          acc[item.status] = item._count.status;
          return acc;
        }, {}),
        typeDistribution: typeDistribution.reduce((acc, item) => {
          acc[item.type] = item._count.type;
          return acc;
        }, {})
      },
      trends: {
        daily: dailyTrend.map(item => ({
          day: item.day,
          count: Number(item.count)
        }))
      },
      performance: {
        duration: {
          avg_minutes: Math.round(durationStats._avg.duration || 0),
          min_minutes: durationStats._min.duration || 0,
          max_minutes: durationStats._max.duration || 0
        }
      }
    };

    res.status(200).json({
      success: true,
      data: { analytics },
      message: 'Appointment analytics retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching appointment analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch appointment analytics',
      error: error.message
    });
  }
});

app.get('/api/analytics/lab-results', async (req, res) => {
  try {
    const { from, to } = req.query;

    if (!from || !to) {
      return res.status(400).json({
        success: false,
        message: 'from and to date parameters are required'
      });
    }

    const fromDate = new Date(from);
    const toDate = new Date(to);

    // Get lab result analytics data
    const [
      totalLabResults,
      testTypeDistribution,
      statusDistribution,
      flaggedResults,
      monthlyTrend
    ] = await Promise.all([
      // Total lab results in date range
      prisma.labResult.count({
        where: {
          isDeleted: false,
          testDate: { gte: fromDate, lte: toDate }
        }
      }),
      // Test type distribution
      prisma.labResult.groupBy({
        by: ['testType'],
        where: {
          isDeleted: false,
          testDate: { gte: fromDate, lte: toDate }
        },
        _count: { testType: true }
      }),
      // Status distribution
      prisma.labResult.groupBy({
        by: ['status'],
        where: {
          isDeleted: false,
          testDate: { gte: fromDate, lte: toDate }
        },
        _count: { status: true }
      }),
      // Flagged results analysis
      prisma.$queryRaw`
        SELECT
          testType as test_type,
          COUNT(*) as total,
          COUNT(CASE WHEN flags IS NOT NULL AND flags != '{}' AND flags != 'null' THEN 1 END) as flagged
        FROM lab_results
        WHERE isDeleted = false
          AND testDate >= ${fromDate}
          AND testDate <= ${toDate}
        GROUP BY testType
        ORDER BY flagged DESC
      `,
      // Monthly trend
      prisma.$queryRaw`
        SELECT
          strftime('%Y-%m-01', testDate) as month,
          COUNT(*) as total,
          COUNT(CASE WHEN flags IS NOT NULL AND flags != '{}' AND flags != 'null' THEN 1 END) as flagged
        FROM lab_results
        WHERE isDeleted = false
          AND testDate >= ${fromDate}
          AND testDate <= ${toDate}
        GROUP BY strftime('%Y-%m-01', testDate)
        ORDER BY month
      `
    ]);

    // Process flagged results to add percentage (convert BigInt to Number)
    const processedFlaggedResults = flaggedResults.map(item => {
      const total = Number(item.total);
      const flagged = Number(item.flagged);
      return {
        testType: item.test_type,
        total: total,
        flagged: flagged,
        percentage: total > 0 ? (flagged / total) * 100 : 0
      };
    });

    const analytics = {
      overview: {
        total: totalLabResults,
        testTypeDistribution: testTypeDistribution.reduce((acc, item) => {
          acc[item.testType] = item._count.testType;
          return acc;
        }, {}),
        statusDistribution: statusDistribution.reduce((acc, item) => {
          acc[item.status] = item._count.status;
          return acc;
        }, {})
      },
      quality: {
        flaggedByTestType: processedFlaggedResults,
        turnaroundTime: { avg_hours: 24, min_hours: 1, max_hours: 72 } // Mock data
      },
      trends: {
        monthly: monthlyTrend.map(item => ({
          month: item.month,
          total: Number(item.total),
          flagged: Number(item.flagged)
        }))
      }
    };

    res.status(200).json({
      success: true,
      data: { analytics },
      message: 'Lab result analytics retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching lab result analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch lab result analytics',
      error: error.message
    });
  }
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint not found',
    path: req.originalUrl,
    availableEndpoints: [
      'GET /health',
      'GET /api',
      'GET /api/stats',
      'GET /api/users',
      'POST /api/users',
      'GET /api/patients',
      'POST /api/patients',
      'GET /api/lab-results',
      'POST /api/lab-results',
      'GET /api/appointments',
      'POST /api/appointments',
      'POST /api/patients/:patientId/assessment-sessions',
      'GET /api/patients/:patientId/assessment-sessions',
      'GET /api/assessment-sessions/:id',
      'GET /api/analytics/patients',
      'GET /api/analytics/appointments',
      'GET /api/analytics/lab-results',
      'GET /api/export/patients',
      'GET /api/export/lab-results',
      'GET /api/export/appointments',
      'GET /api/export/users'
    ]
  });
});

// Global error handler
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  
  // Handle JSON parsing errors specifically
  if (error.type === 'entity.parse.failed') {
    return res.status(400).json({
      success: false,
      message: 'Invalid JSON in request body',
      error: 'The request body contains malformed JSON',
      timestamp: new Date().toISOString()
    });
  }
  
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong',
    timestamp: new Date().toISOString()
  });
});

// Create test admin user on startup
async function createTestUser() {
  try {
    const existingAdmin = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (!existingAdmin) {
      // Check if username already exists
      const existingUsername = await prisma.user.findUnique({
        where: { username: 'admin' }
      });

      if (existingUsername) {
        console.log('✅ Admin user with username "admin" already exists');
        return;
      }

      const hashedPassword = await bcrypt.hash('admin123', 12);
      await prisma.user.create({
        data: {
          username: 'admin',
          email: '<EMAIL>',
          password: hashedPassword,
          firstName: 'Admin',
          lastName: 'User',
          role: 'ADMIN'
        }
      });
      console.log('✅ Test admin user created: <EMAIL> / admin123');
    } else {
      console.log('✅ Test admin user already exists: <EMAIL>');
    }
  } catch (error) {
    console.log('⚠️ Note: Could not create test user (this is not critical):', error.message);
  }
}

// Start server
const server = app.listen(PORT, '0.0.0.0', async () => {
  console.log(`🚀 Psychiatry App Server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV}`);
  console.log(`🔗 Health Check: http://localhost:${PORT}/health`);
  console.log(`🔗 API Info: http://localhost:${PORT}/api`);
  console.log(`🔗 Database Stats: http://localhost:${PORT}/api/stats`);
  console.log(`📱 Ready to serve frontend at http://localhost:5173`);

  // Create test user
  try {
    await createTestUser();
  } catch (error) {
    console.log('⚠️ Test user creation failed, but server is still running');
  }

  console.log('✨ All core features are working!');
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully');
  await prisma.$disconnect();
  server.close(() => {
    console.log('Process terminated');
  });
});

process.on('SIGINT', async () => {
  console.log('SIGINT received, shutting down gracefully');
  await prisma.$disconnect();
  server.close(() => {
    console.log('Process terminated');
  });
});

module.exports = app;
