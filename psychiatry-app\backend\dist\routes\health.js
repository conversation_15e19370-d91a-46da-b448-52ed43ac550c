"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const client_1 = require("@prisma/client");
const router = (0, express_1.Router)();
const prisma = new client_1.PrismaClient();
router.get('/', async (req, res) => {
    try {
        await prisma.$queryRaw `SELECT 1`;
        res.status(200).json({
            success: true,
            message: 'Server is healthy',
            timestamp: new Date().toISOString(),
            environment: process.env.NODE_ENV || 'development',
            database: 'connected',
            version: '1.0.0'
        });
    }
    catch (error) {
        res.status(503).json({
            success: false,
            message: 'Server is unhealthy',
            timestamp: new Date().toISOString(),
            environment: process.env.NODE_ENV || 'development',
            database: 'disconnected',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
router.get('/detailed', async (req, res) => {
    try {
        const startTime = Date.now();
        const [patientCount, labResultCount, appointmentCount] = await Promise.all([
            prisma.patient.count({ where: { isDeleted: false } }),
            prisma.labResult.count({ where: { isDeleted: false } }),
            prisma.appointment.count()
        ]);
        const dbResponseTime = Date.now() - startTime;
        const memoryUsage = process.memoryUsage();
        const totalMemory = memoryUsage.heapTotal;
        const usedMemory = memoryUsage.heapUsed;
        const memoryPercentage = Math.round((usedMemory / totalMemory) * 100);
        res.status(200).json({
            success: true,
            timestamp: new Date().toISOString(),
            system: {
                uptime: process.uptime(),
                memory: {
                    used: Math.round(usedMemory / 1024 / 1024),
                    total: Math.round(totalMemory / 1024 / 1024),
                    percentage: memoryPercentage
                },
                nodeVersion: process.version,
                platform: process.platform
            },
            database: {
                status: 'connected',
                responseTime: dbResponseTime,
                patientCount,
                labResultCount,
                appointmentCount
            },
            environment: process.env.NODE_ENV || 'development',
            version: '1.0.0'
        });
    }
    catch (error) {
        res.status(503).json({
            success: false,
            timestamp: new Date().toISOString(),
            error: error instanceof Error ? error.message : 'Unknown error',
            database: {
                status: 'disconnected'
            }
        });
    }
});
router.get('/readiness', async (req, res) => {
    try {
        await prisma.$queryRaw `SELECT 1`;
        await prisma.user.findFirst();
        res.status(200).json({
            success: true,
            message: 'Service is ready',
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        res.status(503).json({
            success: false,
            message: 'Service is not ready',
            timestamp: new Date().toISOString(),
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
router.get('/liveness', (req, res) => {
    res.status(200).json({
        success: true,
        message: 'Service is alive',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});
exports.default = router;
//# sourceMappingURL=health.js.map