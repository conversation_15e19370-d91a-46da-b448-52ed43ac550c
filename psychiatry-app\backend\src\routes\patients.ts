import { Router } from 'express';
import { PatientController } from '@/controllers/patientController';
import { LabResultController } from '@/controllers/labResultController';
import { AssessmentController } from '@/controllers/assessmentController';
import { authenticate, authorize } from '@/middleware/auth';

/**
 * @swagger
 * tags:
 *   name: Patients
 *   description: Patient management endpoints for psychiatric care
 */

/**
 * Patient routes
 * Base path: /api/patients
 */

const router = Router();

// All patient routes require authentication
router.use(authenticate);

/**
 * @swagger
 * /api/patients/stats:
 *   get:
 *     summary: Get patient statistics
 *     description: Retrieve statistical information about patients for dashboard and analytics
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Patient statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                           description: Total number of patients
 *                         active:
 *                           type: integer
 *                           description: Number of active patients
 *                         newThisMonth:
 *                           type: integer
 *                           description: New patients this month
 *                         byGender:
 *                           type: object
 *                           description: Patient count by gender
 *                         byAgeGroup:
 *                           type: object
 *                           description: Patient count by age group
 *       401:
 *         description: Authentication required
 *       500:
 *         description: Internal server error
 */
router.get('/stats', PatientController.getPatientStats);

/**
 * @route   GET /api/patients
 * @desc    Get patients with pagination, search, and filtering
 * @access  Private (All authenticated users)
 * @query   page, limit, search, gender, isActive, sortBy, sortOrder
 */
router.get('/', PatientController.getPatients);

/**
 * @route   POST /api/patients
 * @desc    Create a new patient
 * @access  Private (ADMIN, CLINICIAN)
 * @body    Patient data (firstName, lastName, dateOfBirth, gender, etc.)
 */
router.post('/', authorize(['ADMIN', 'CLINICIAN']), PatientController.createPatient);

/**
 * @route   GET /api/patients/:id
 * @desc    Get patient by ID
 * @access  Private (All authenticated users)
 */
router.get('/:id', PatientController.getPatientById);

/**
 * @route   PUT /api/patients/:id
 * @desc    Update patient
 * @access  Private (ADMIN, CLINICIAN)
 */
router.put('/:id', authorize(['ADMIN', 'CLINICIAN']), PatientController.updatePatient);

/**
 * @route   DELETE /api/patients/:id
 * @desc    Delete patient (soft delete)
 * @access  Private (ADMIN only)
 */
router.delete('/:id', authorize(['ADMIN']), PatientController.deletePatient);

/**
 * @route   GET /api/patients/:patientId/lab-results
 * @desc    Get lab results for a specific patient
 * @access  Private (All authenticated users)
 * @query   testType, limit, dateFrom, dateTo
 */
router.get('/:patientId/lab-results', LabResultController.getPatientLabResults);

/**
 * @route   GET /api/patients/:patientId/lab-results/trends
 * @desc    Get lab result trends for a patient
 * @access  Private (All authenticated users)
 * @query   testType, dateFrom, dateTo
 */
router.get('/:patientId/lab-results/trends', LabResultController.getLabResultTrends);

/**
 * @route   POST /api/patients/:patientId/assessment-sessions
 * @desc    Create a new assessment session for a patient
 * @access  Private (ADMIN, CLINICIAN)
 */
router.post('/:patientId/assessment-sessions',
  authorize(['ADMIN', 'CLINICIAN']),
  AssessmentController.createAssessmentSession
);

/**
 * @route   GET /api/patients/:patientId/assessment-sessions
 * @desc    Get assessment sessions for a patient
 * @access  Private (All authenticated users)
 */
router.get('/:patientId/assessment-sessions',
  AssessmentController.getPatientAssessmentSessions
);

export default router;
