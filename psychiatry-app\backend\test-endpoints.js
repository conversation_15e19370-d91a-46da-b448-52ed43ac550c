const axios = require('axios');

const API_BASE_URL = 'http://localhost:3002';

async function testEndpoints() {
  try {
    console.log('🧪 Testing API endpoints...\n');

    // Step 1: Login to get authentication token
    console.log('1️⃣ Testing login...');
    const loginResponse = await axios.post(`${API_BASE_URL}/api/auth/login`, {
      username: 'dr.smith',
      password: 'clinician123!'
    });

    if (loginResponse.data.success) {
      console.log('✅ Login successful');
      const token = loginResponse.data.data.accessToken;
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      };

      // Step 2: Test patient lookup
      console.log('\n2️⃣ Testing patient lookup...');
      const patientResponse = await axios.get(`${API_BASE_URL}/api/patients/P12345`, { headers });
      
      if (patientResponse.data.success) {
        console.log('✅ Patient P12345 found:', patientResponse.data.data.patient.firstName, patientResponse.data.data.patient.lastName);
      } else {
        console.log('❌ Patient lookup failed:', patientResponse.data.message);
      }

      // Step 3: Test assessment session creation
      console.log('\n3️⃣ Testing assessment session creation...');
      const assessmentData = {
        patientId: 'P12345',
        sessionDate: new Date().toISOString(),
        assessments: [
          {
            disorderId: 'major-depressive-disorder',
            severity: 'moderate',
            confidence: 0.8,
            symptoms: ['depressed mood', 'loss of interest', 'fatigue'],
            notes: 'Patient shows classic symptoms of MDD'
          }
        ],
        clinicalImpression: 'Patient presents with moderate depression symptoms',
        riskAssessment: {
          suicidalIdeation: false,
          selfHarm: false,
          riskLevel: 'low'
        },
        status: 'completed',
        duration: 60
      };

      const assessmentResponse = await axios.post(
        `${API_BASE_URL}/api/patients/P12345/assessment-sessions`,
        assessmentData,
        { headers }
      );

      if (assessmentResponse.data.success) {
        console.log('✅ Assessment session created successfully');
        console.log('   Session ID:', assessmentResponse.data.data.id);
      } else {
        console.log('❌ Assessment session creation failed:', assessmentResponse.data.message);
      }

      // Step 4: Test analytics endpoint (this should fail with BigInt error)
      console.log('\n4️⃣ Testing analytics endpoint...');
      try {
        const analyticsResponse = await axios.get(
          `${API_BASE_URL}/api/analytics/lab-results?from=2024-01-01&to=2025-12-31`,
          { headers }
        );
        
        if (analyticsResponse.data.success) {
          console.log('✅ Analytics endpoint working');
        } else {
          console.log('❌ Analytics endpoint failed:', analyticsResponse.data.message);
        }
      } catch (analyticsError) {
        console.log('❌ Analytics endpoint error:', analyticsError.response?.data?.error || analyticsError.message);
      }

      // Step 5: Test lab results creation
      console.log('\n5️⃣ Testing lab results creation...');
      const labResultData = {
        patientId: 'P12345',
        testType: 'Complete Blood Count',
        testDate: new Date().toISOString(),
        orderedBy: 'Dr. Smith',
        labName: 'Springfield Lab',
        results: {
          'WBC': '7.2',
          'RBC': '4.5',
          'Hemoglobin': '14.2',
          'Hematocrit': '42.1'
        },
        normalRanges: {
          'WBC': '4.0-11.0',
          'RBC': '4.2-5.4',
          'Hemoglobin': '12.0-16.0',
          'Hematocrit': '36.0-46.0'
        },
        flags: {},
        notes: 'Normal results',
        status: 'COMPLETED'
      };

      try {
        const labResponse = await axios.post(
          `${API_BASE_URL}/api/lab-results`,
          labResultData,
          { headers }
        );

        if (labResponse.data.success) {
          console.log('✅ Lab result created successfully');
        } else {
          console.log('❌ Lab result creation failed:', labResponse.data.message);
        }
      } catch (labError) {
        console.log('❌ Lab result creation error:', labError.response?.data?.error || labError.message);
      }

    } else {
      console.log('❌ Login failed:', loginResponse.data.message);
    }

  } catch (error) {
    console.error('❌ Test error:', error.response?.data || error.message);
  }
}

testEndpoints();
